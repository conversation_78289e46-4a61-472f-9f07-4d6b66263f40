<?php
/**
 * Diagnostic très détaillé pour capturer l'erreur exacte de validation
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Fonction pour capturer TOUTES les erreurs
function detailedErrorHandler($errno, $errstr, $errfile, $errline) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "<strong>ERREUR PHP:</strong> " . htmlspecialchars($errstr) . "<br>";
    echo "<strong>Fichier:</strong> " . basename($errfile) . ":" . $errline . "<br>";
    echo "</div>";
    return true;
}

set_error_handler('detailedErrorHandler');

echo "<h1>🔍 Diagnostic Détaillé - Validation Commande</h1>";

// Démarrer session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h2>1. Test de connexion directe</h2>";
try {
    $pdo = new PDO('odbc:DataCafe', 'admin', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "✅ Connexion PDO directe OK<br>";
} catch (Exception $e) {
    echo "❌ Erreur connexion: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>2. Chargement pos_config.php</h2>";
try {
    require_once 'pos_config.php';
    echo "✅ pos_config.php chargé<br>";
    
    if ($pos->isConnected()) {
        echo "✅ POS connecté<br>";
    } else {
        echo "❌ POS non connecté<br>";
        exit;
    }
} catch (Exception $e) {
    echo "❌ Erreur chargement pos_config: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>3. Préparation panier minimal</h2>";
try {
    $pos->clearCart();
    echo "✅ Panier vidé<br>";
    
    // Récupérer UN article simple
    $sql = "SELECT TOP 1 IDarticles, designation, prix, IDCategorie FROM articles WHERE prix > 0";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $article = $stmt->fetch();
    
    if (!$article) {
        echo "❌ Aucun article trouvé<br>";
        exit;
    }
    
    echo "✅ Article sélectionné: {$article['designation']} (ID: {$article['IDarticles']}, Prix: {$article['prix']})<br>";
    
    // Ajouter au panier
    $result = $pos->addToCart($article['IDarticles'], 1);
    if ($result === true || (is_array($result) && $result['success'])) {
        echo "✅ Article ajouté au panier<br>";
    } else {
        echo "❌ Erreur ajout panier: " . (is_array($result) ? $result['message'] : 'Erreur inconnue') . "<br>";
        exit;
    }
    
    $cart = $pos->getCart();
    echo "✅ Panier contient: " . count($cart) . " article(s)<br>";
    
} catch (Exception $e) {
    echo "❌ Erreur préparation panier: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>4. Test étape par étape de processOrder</h2>";

try {
    echo "<h3>4.1. Début de processOrder</h3>";
    $cart = $pos->getCart();
    echo "Panier avant processOrder: " . count($cart) . " articles<br>";
    
    echo "<h3>4.2. Test de saveOrder directement</h3>";
    
    // Appeler saveOrder directement pour capturer l'erreur
    echo "Tentative d'appel saveOrder...<br>";
    
    // Utiliser la réflexion pour accéder à la méthode privée
    $reflection = new ReflectionClass($pos);
    $saveOrderMethod = $reflection->getMethod('saveOrder');
    $saveOrderMethod->setAccessible(true);
    
    $ticketId = $saveOrderMethod->invoke($pos, $cart, 'cash', 1, 103);
    
    if ($ticketId) {
        echo "✅ saveOrder réussi, ticket ID: $ticketId<br>";
    } else {
        echo "❌ saveOrder a échoué<br>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ ERREUR CAPTURÉE dans saveOrder:</h4>";
    echo "<strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>Fichier:</strong> " . $e->getFile() . "<br>";
    echo "<strong>Ligne:</strong> " . $e->getLine() . "<br>";
    echo "<strong>Code erreur:</strong> " . $e->getCode() . "<br>";
    echo "<h4>Stack trace:</h4>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}

echo "<h2>5. Test manuel des requêtes SQL</h2>";

echo "<h3>5.1. Test requête tickets</h3>";
try {
    $sql = "SELECT MAX(NumTick) as last_num FROM tickets";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    $nextNumTick = ($result['last_num'] ?? 0) + 1;
    echo "✅ Prochain NumTick: $nextNumTick<br>";
} catch (Exception $e) {
    echo "❌ Erreur requête tickets: " . $e->getMessage() . "<br>";
}

echo "<h3>5.2. Test INSERT tickets</h3>";
try {
    $dateNum = intval(date('Ymd'));
    $timeNum = intval(date('His'));
    $total = 10.50; // Prix fictif
    
    $sql = "INSERT INTO tickets (NumTick, IDServeur, IDTables, DATE, heure, total, typepaye, Djr, ImpTick) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($sql);
    echo "✅ Requête INSERT tickets préparée<br>";
    
    // Ne pas exécuter, juste tester la préparation
    
} catch (Exception $e) {
    echo "❌ Erreur préparation INSERT tickets: " . $e->getMessage() . "<br>";
}

echo "<h3>5.3. Test INSERT VteJour</h3>";
try {
    $sql = "INSERT INTO VteJour (articles, quantite, total, IDarticle, NumCategories, Cuisine, IDtickets) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($sql);
    echo "✅ Requête INSERT VteJour préparée<br>";
    
} catch (Exception $e) {
    echo "❌ Erreur préparation INSERT VteJour: " . $e->getMessage() . "<br>";
    echo "Message détaillé: " . htmlspecialchars($e->getMessage()) . "<br>";
}

echo "<h2>6. Vérification structure exacte VteJour</h2>";
try {
    // Méthode alternative pour voir la structure
    $sql = "SELECT * FROM VteJour WHERE 1=0";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    
    echo "Colonnes VteJour:<br>";
    for ($i = 0; $i < $stmt->columnCount(); $i++) {
        $meta = $stmt->getColumnMeta($i);
        echo "- " . $meta['name'] . " (" . ($meta['native_type'] ?? 'unknown') . ")<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur structure VteJour: " . $e->getMessage() . "<br>";
}

echo "<h2>7. Test avec données réelles</h2>";
if (isset($article)) {
    echo "Test avec article réel:<br>";
    echo "- ID: {$article['IDarticles']}<br>";
    echo "- Nom: {$article['designation']}<br>";
    echo "- Prix: {$article['prix']}<br>";
    echo "- Catégorie: {$article['IDCategorie']}<br>";
    
    try {
        // Test INSERT VteJour avec données réelles
        $sql = "INSERT INTO VteJour (articles, quantite, total, IDarticle, NumCategories, Cuisine, IDtickets) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($sql);
        
        // Données de test (sans exécuter)
        $testData = [
            $article['designation'],
            1,
            $article['prix'],
            $article['IDarticles'],
            $article['IDCategorie'],
            '',
            999999 // ID fictif
        ];
        
        echo "✅ Données de test préparées pour VteJour<br>";
        echo "Données: " . implode(', ', $testData) . "<br>";
        
    } catch (Exception $e) {
        echo "❌ Erreur test données VteJour: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>✅ Diagnostic terminé</h2>";
echo "<p>Vérifiez les erreurs capturées ci-dessus pour identifier le problème exact.</p>";
?>
