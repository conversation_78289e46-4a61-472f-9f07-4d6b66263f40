<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX Validation Commande</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🧪 Test AJAX Validation Commande</h1>
    
    <div class="test-section">
        <h3>1. Test de détection AJAX</h3>
        <button onclick="testAjaxDetection()">Tester détection AJAX</button>
        <div id="ajax-result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Test ajout article au panier</h3>
        <button onclick="testAddToCart()">Ajouter article au panier</button>
        <div id="cart-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Test validation commande</h3>
        <button onclick="testProcessOrder()">Valider commande</button>
        <div id="order-result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Test vider panier</h3>
        <button onclick="testClearCart()">Vider panier</button>
        <div id="clear-result"></div>
    </div>
    
    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'test-section ' + type;
            div.innerHTML = '<strong>' + new Date().toLocaleTimeString() + ':</strong> ' + message;
            results.appendChild(div);
        }

        function testAjaxDetection() {
            log('Test de détection AJAX...', 'info');
            
            const formData = new FormData();
            formData.append('action', 'test_ajax');
            
            fetch('pos_mobile.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                log('Réponse AJAX reçue (longueur: ' + html.length + ' caractères)', 'success');
                
                // Vérifier si la réponse contient une redirection
                if (html.includes('Location:') || html.includes('header(')) {
                    log('⚠️ Redirection détectée dans la réponse', 'error');
                } else {
                    log('✅ Pas de redirection, AJAX fonctionne', 'success');
                }
                
                document.getElementById('ajax-result').innerHTML = '<pre>' + html.substring(0, 500) + '...</pre>';
            })
            .catch(error => {
                log('❌ Erreur AJAX: ' + error.message, 'error');
                document.getElementById('ajax-result').innerHTML = 'Erreur: ' + error.message;
            });
        }

        function testAddToCart() {
            log('Test ajout au panier...', 'info');
            
            const formData = new FormData();
            formData.append('action', 'add_to_cart');
            formData.append('article_id', '1');
            formData.append('quantity', '1');
            
            fetch('pos_mobile.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                log('Article ajouté au panier', 'success');
                
                // Parser la réponse pour vérifier le compteur
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const cartCount = doc.querySelector('#cartCount');
                
                if (cartCount) {
                    log('Compteur panier: ' + cartCount.textContent, 'info');
                } else {
                    log('⚠️ Compteur panier non trouvé', 'error');
                }
                
                document.getElementById('cart-result').innerHTML = 'Article ajouté, compteur: ' + (cartCount ? cartCount.textContent : 'N/A');
            })
            .catch(error => {
                log('❌ Erreur ajout panier: ' + error.message, 'error');
                document.getElementById('cart-result').innerHTML = 'Erreur: ' + error.message;
            });
        }

        function testProcessOrder() {
            log('Test validation commande...', 'info');
            
            const formData = new FormData();
            formData.append('action', 'process_order');
            formData.append('payment_method', 'cash');
            
            fetch('pos_mobile.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                log('Réponse validation reçue', 'info');
                
                // Parser la réponse pour vérifier les messages
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                
                const successMessage = doc.querySelector('.alert-success');
                const errorMessage = doc.querySelector('.alert-danger');
                const cartItems = doc.querySelector('#cartItems');
                
                if (successMessage) {
                    log('✅ Message de succès trouvé: ' + successMessage.textContent.trim(), 'success');
                } else if (errorMessage) {
                    log('❌ Message d\'erreur trouvé: ' + errorMessage.textContent.trim(), 'error');
                } else {
                    log('⚠️ Aucun message de statut trouvé', 'error');
                }
                
                if (cartItems && cartItems.querySelector('.empty-cart')) {
                    log('✅ Panier vide après validation', 'success');
                } else {
                    log('⚠️ Panier non vide après validation', 'error');
                }
                
                document.getElementById('order-result').innerHTML = 
                    'Succès: ' + (successMessage ? 'Oui' : 'Non') + 
                    ', Erreur: ' + (errorMessage ? 'Oui' : 'Non') + 
                    ', Panier vide: ' + (cartItems && cartItems.querySelector('.empty-cart') ? 'Oui' : 'Non');
            })
            .catch(error => {
                log('❌ Erreur validation: ' + error.message, 'error');
                document.getElementById('order-result').innerHTML = 'Erreur: ' + error.message;
            });
        }

        function testClearCart() {
            log('Test vider panier...', 'info');
            
            const formData = new FormData();
            formData.append('action', 'clear_cart');
            
            fetch('pos_mobile.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                log('Panier vidé', 'success');
                
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const cartCount = doc.querySelector('#cartCount');
                
                document.getElementById('clear-result').innerHTML = 'Panier vidé, compteur: ' + (cartCount ? cartCount.textContent : 'N/A');
            })
            .catch(error => {
                log('❌ Erreur vider panier: ' + error.message, 'error');
                document.getElementById('clear-result').innerHTML = 'Erreur: ' + error.message;
            });
        }

        // Auto-test au chargement
        window.onload = function() {
            log('Page de test chargée', 'info');
        };
    </script>
</body>
</html>
