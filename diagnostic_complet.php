<?php
/**
 * Diagnostic complet étape par étape
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Diagnostic Complet - Validation Commande</h1>";

// Étape 1: Test de base
echo "<h2>📋 Étape 1: Vérifications de base</h2>";

// Vérifier si pos_config.php existe et se charge
if (!file_exists('pos_config.php')) {
    echo "❌ pos_config.php n'existe pas<br>";
    exit;
}

try {
    require_once 'pos_config.php';
    echo "✅ pos_config.php chargé<br>";
} catch (Exception $e) {
    echo "❌ Erreur lors du chargement de pos_config.php: " . $e->getMessage() . "<br>";
    exit;
}

// Vérifier la classe POSConfig
if (!class_exists('POSConfig')) {
    echo "❌ Classe POSConfig non trouvée<br>";
    exit;
}

$pos = new POSConfig();
echo "✅ Instance POSConfig créée<br>";

// Étape 2: Test de connexion
echo "<h2>🔌 Étape 2: Test de connexion</h2>";

if (!$pos->isConnected()) {
    echo "❌ Pas de connexion à la base de données<br>";
    echo "DSN: " . POS_DSN . "<br>";
    echo "Username: " . POS_USERNAME . "<br>";
    exit;
}
echo "✅ Connexion à la base réussie<br>";

// Étape 3: Test des tables
echo "<h2>🗃️ Étape 3: Vérification des tables</h2>";

// Test table tickets
try {
    $sql = "SELECT COUNT(*) as count FROM tickets";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "✅ Table tickets accessible (" . $result['count'] . " enregistrements)<br>";
} catch (Exception $e) {
    echo "❌ Erreur table tickets: " . $e->getMessage() . "<br>";
    exit;
}

// Test table VteJour
try {
    $sql = "SELECT COUNT(*) as count FROM VteJour";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "✅ Table VteJour accessible (" . $result['count'] . " enregistrements)<br>";
} catch (Exception $e) {
    echo "❌ Erreur table VteJour: " . $e->getMessage() . "<br>";
    exit;
}

// Test table articles
try {
    $sql = "SELECT COUNT(*) as count FROM articles";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "✅ Table articles accessible (" . $result['count'] . " enregistrements)<br>";
} catch (Exception $e) {
    echo "❌ Erreur table articles: " . $e->getMessage() . "<br>";
    exit;
}

// Étape 4: Test du panier
echo "<h2>🛒 Étape 4: Test du panier</h2>";

// Vider le panier
$pos->clearCart();
echo "✅ Panier vidé<br>";

// Récupérer un article
try {
    $sql = "SELECT TOP 1 IDarticle, designation, prix, IDCategorie, Cuisine FROM articles WHERE prix > 0";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $article = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$article) {
        echo "❌ Aucun article trouvé<br>";
        exit;
    }
    
    echo "✅ Article trouvé: " . $article['designation'] . " (ID: " . $article['IDarticle'] . ", Prix: " . $article['prix'] . ")<br>";
    
    // Ajouter au panier
    $pos->addToCart($article['IDarticle'], 2);
    $cart = $pos->getCart();
    
    if (empty($cart)) {
        echo "❌ Impossible d'ajouter au panier<br>";
        exit;
    }
    
    echo "✅ Article ajouté au panier (quantité: 2)<br>";
    
} catch (Exception $e) {
    echo "❌ Erreur lors de la récupération d'article: " . $e->getMessage() . "<br>";
    exit;
}

// Étape 5: Test insertion manuelle ticket
echo "<h2>🎫 Étape 5: Test insertion manuelle ticket</h2>";

try {
    // Récupérer le prochain NumTick
    $sql = "SELECT MAX(NumTick) as last_num FROM tickets";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    $nextNumTick = ($result['last_num'] ?? 0) + 1;
    echo "✅ Prochain NumTick: $nextNumTick<br>";
    
    // Préparer les données
    $dateNum = intval(date('Ymd'));
    $timeNum = intval(date('His'));
    $total = 25.50;
    
    echo "Données à insérer:<br>";
    echo "- NumTick: $nextNumTick<br>";
    echo "- IDServeur: 1<br>";
    echo "- IDTables: 103<br>";
    echo "- DATE: $dateNum<br>";
    echo "- heure: $timeNum<br>";
    echo "- total: $total<br>";
    echo "- typepaye: ESP<br>";
    echo "- Djr: 1<br>";
    echo "- ImpTick: 1<br>";
    
    // Tenter l'insertion
    $sql = "INSERT INTO tickets (NumTick, IDServeur, IDTables, DATE, heure, total, typepaye, Djr, ImpTick) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $pos->pdo->prepare($sql);
    
    $insertData = [
        intval($nextNumTick),
        1,
        103,
        $dateNum,
        $timeNum,
        $total,
        'ESP',
        1,
        1
    ];
    
    $success = $stmt->execute($insertData);
    
    if ($success) {
        $ticketId = $pos->pdo->lastInsertId();
        echo "✅ Insertion ticket réussie! ID: $ticketId<br>";
        
        // Supprimer le ticket de test
        $sql = "DELETE FROM tickets WHERE IDtickets = ?";
        $stmt = $pos->pdo->prepare($sql);
        $stmt->execute([$ticketId]);
        echo "✅ Ticket de test supprimé<br>";
        
    } else {
        $errorInfo = $stmt->errorInfo();
        echo "❌ Erreur insertion ticket:<br>";
        echo "SQLSTATE: " . $errorInfo[0] . "<br>";
        echo "Code erreur: " . $errorInfo[1] . "<br>";
        echo "Message: " . $errorInfo[2] . "<br>";
        exit;
    }
    
} catch (Exception $e) {
    echo "❌ Exception insertion ticket: " . $e->getMessage() . "<br>";
    exit;
}

// Étape 6: Test processOrder
echo "<h2>⚙️ Étape 6: Test processOrder</h2>";

try {
    echo "Contenu du panier avant processOrder:<br>";
    $cart = $pos->getCart();
    foreach ($cart as $id => $item) {
        echo "- Article: {$item['article']['designation']}, Qté: {$item['quantity']}, Prix: {$item['price']}<br>";
    }
    
    echo "Lancement de processOrder...<br>";
    $orderId = $pos->processOrder('cash');
    
    if ($orderId) {
        echo "✅ processOrder réussi! ID: $orderId<br>";
        
        // Vérifier que le ticket a été créé
        $sql = "SELECT * FROM tickets WHERE IDtickets = ?";
        $stmt = $pos->pdo->prepare($sql);
        $stmt->execute([$orderId]);
        $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($ticket) {
            echo "✅ Ticket créé en base:<br>";
            foreach ($ticket as $key => $value) {
                echo "  - $key: $value<br>";
            }
        } else {
            echo "❌ Ticket non trouvé en base<br>";
        }
        
        // Vérifier les détails VteJour
        $sql = "SELECT * FROM VteJour WHERE IDtickets = ?";
        $stmt = $pos->pdo->prepare($sql);
        $stmt->execute([$orderId]);
        $details = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "✅ Détails VteJour (" . count($details) . " lignes):<br>";
        foreach ($details as $detail) {
            echo "  - Article: {$detail['articles']}, Qté: {$detail['quantite']}, Total: {$detail['total']}<br>";
        }
        
    } else {
        echo "❌ processOrder a échoué<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Exception processOrder: " . $e->getMessage() . "<br>";
}

// Étape 7: Vérification du panier après
echo "<h2>🔍 Étape 7: État final du panier</h2>";
$cart = $pos->getCart();
if (empty($cart)) {
    echo "✅ Panier vide après processOrder<br>";
} else {
    echo "❌ Panier non vide après processOrder (" . count($cart) . " articles)<br>";
    foreach ($cart as $id => $item) {
        echo "- Article: {$item['article']['designation']}, Qté: {$item['quantity']}<br>";
    }
}

echo "<h2>📊 Résumé</h2>";
echo "Si tous les tests sont ✅, le problème vient de l'interface AJAX.<br>";
echo "Si un test est ❌, le problème est identifié à cette étape.<br>";

?>
