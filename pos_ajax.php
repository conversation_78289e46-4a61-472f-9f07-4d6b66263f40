<?php
/**
 * Gestionnaire AJAX pour le POS
 * Gère les actions du panier et autres opérations
 */

require_once 'pos_config.php';

// Définir le type de contenu JSON
header('Content-Type: application/json');

// Vérifier la connexion
if (!$pos->isConnected()) {
    echo json_encode(['success' => false, 'message' => 'Erreur de connexion à la base de données']);
    exit;
}

// Récupérer l'action demandée
$action = $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'add_to_cart':
            $articleId = intval($_POST['article_id'] ?? 0);
            $quantity = intval($_POST['quantity'] ?? 1);
            $allowInsufficientStock = isset($_POST['allow_insufficient_stock']) ? 
                                    (bool)$_POST['allow_insufficient_stock'] : true;
            
            if ($articleId <= 0) {
                echo json_encode(['success' => false, 'message' => 'ID article invalide']);
                exit;
            }
            
            $result = $pos->addToCart($articleId, $quantity, $allowInsufficientStock);
            echo json_encode($result);
            break;
            
        case 'remove_from_cart':
            $articleId = intval($_POST['article_id'] ?? 0);
            
            if ($articleId <= 0) {
                echo json_encode(['success' => false, 'message' => 'ID article invalide']);
                exit;
            }
            
            $success = $pos->removeFromCart($articleId);
            echo json_encode([
                'success' => $success,
                'message' => $success ? 'Article supprimé du panier' : 'Erreur lors de la suppression'
            ]);
            break;
            
        case 'update_cart_quantity':
            $articleId = intval($_POST['article_id'] ?? 0);
            $quantity = intval($_POST['quantity'] ?? 0);
            
            if ($articleId <= 0) {
                echo json_encode(['success' => false, 'message' => 'ID article invalide']);
                exit;
            }
            
            $success = $pos->updateCartQuantity($articleId, $quantity);
            echo json_encode([
                'success' => $success,
                'message' => $success ? 'Quantité mise à jour' : 'Erreur lors de la mise à jour'
            ]);
            break;
            
        case 'get_cart':
            $cart = $pos->getCart();
            $total = $pos->getCartTotal();
            $tax = $pos->getCartTax();
            $totalTTC = $pos->getCartTotalTTC();
            
            echo json_encode([
                'success' => true,
                'cart' => $cart,
                'totals' => [
                    'subtotal' => $total,
                    'tax' => $tax,
                    'total' => $totalTTC
                ],
                'item_count' => array_sum(array_column($cart, 'quantity'))
            ]);
            break;
            
        case 'clear_cart':
            $pos->clearCart();
            echo json_encode(['success' => true, 'message' => 'Panier vidé']);
            break;
            
        case 'process_order':
            $paymentMethod = $_POST['payment_method'] ?? 'cash';
            
            $orderId = $pos->processOrder($paymentMethod);
            
            if ($orderId) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Commande traitée avec succès',
                    'order_id' => $orderId
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors du traitement de la commande'
                ]);
            }
            break;
            
        case 'search_articles':
            $search = $_GET['search'] ?? '';
            $includeZeroStock = isset($_GET['include_zero_stock']) ? 
                              (bool)$_GET['include_zero_stock'] : true;
            
            if (strlen($search) < 2) {
                echo json_encode(['success' => false, 'message' => 'Recherche trop courte']);
                exit;
            }
            
            $articles = $pos->searchArticles($search, $includeZeroStock);
            
            // Ajouter les URLs d'images
            foreach ($articles as &$article) {
                $article['image_url'] = $pos->hasArticleImage($article['IDarticles']) ? 
                                      $pos->getArticleImageUrl($article['IDarticles']) : null;
                $article['price'] = $pos->getArticlePrice($article['IDarticles']);
            }
            
            echo json_encode([
                'success' => true,
                'articles' => $articles,
                'count' => count($articles)
            ]);
            break;
            
        case 'get_categories':
            $categories = $pos->getCategories();
            echo json_encode([
                'success' => true,
                'categories' => $categories
            ]);
            break;
            
        case 'get_articles_by_category':
            $categoryId = isset($_GET['category_id']) ? intval($_GET['category_id']) : null;
            $includeZeroStock = isset($_GET['include_zero_stock']) ? 
                              (bool)$_GET['include_zero_stock'] : true;
            
            $articles = $pos->getArticlesByCategory($categoryId, $includeZeroStock);
            
            // Ajouter les URLs d'images et prix
            foreach ($articles as &$article) {
                $article['image_url'] = $pos->hasArticleImage($article['IDarticles']) ? 
                                      $pos->getArticleImageUrl($article['IDarticles']) : null;
                $article['price'] = $pos->getArticlePrice($article['IDarticles']);
            }
            
            echo json_encode([
                'success' => true,
                'articles' => $articles,
                'count' => count($articles)
            ]);
            break;
            
        case 'get_today_stats':
            $stats = $pos->getTodayStats();
            echo json_encode([
                'success' => true,
                'stats' => $stats
            ]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Action non reconnue']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Erreur pos_ajax.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Erreur serveur: ' . $e->getMessage()
    ]);
}
?>
