<?php
/**
 * <PERSON>ript pour isoler temporairement les fichiers qui contiennent
 * des requêtes SQL avec "IDarticle" incorrect
 */

echo "<h1>🔒 Isolation des fichiers problématiques</h1>";

$problematicFiles = [
    'diagnostic_complet.php' => 'diagnostic_complet.php.disabled',
    'test_direct.php' => 'test_direct.php.disabled'
];

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<h3>⚠️ Pourquoi isoler ces fichiers ?</h3>";
echo "<p>Ces fichiers contiennent des requêtes SQL avec 'IDarticle' (sans 's') au lieu de 'IDarticles' (avec 's').</p>";
echo "<p>Même si nous les avons corrigés, ils pourraient encore causer des problèmes s'ils sont inclus ou exécutés automatiquement.</p>";
echo "</div>";

echo "<h2>1. Renommage des fichiers problématiques</h2>";

foreach ($problematicFiles as $original => $disabled) {
    if (file_exists($original)) {
        if (rename($original, $disabled)) {
            echo "✅ $original → $disabled<br>";
        } else {
            echo "❌ Impossible de renommer $original<br>";
        }
    } else {
        echo "ℹ️ $original n'existe pas<br>";
    }
}

echo "<h2>2. Vérification des autres fichiers suspects</h2>";

// Chercher d'autres fichiers qui pourraient contenir "IDarticle"
$phpFiles = glob('*.php');
$suspiciousFiles = [];

foreach ($phpFiles as $file) {
    // Ignorer les fichiers que nous venons de créer
    if (in_array($file, ['debug_pos_mobile_error.php', 'clear_cache_and_sessions.php', 'isolate_problematic_files.php', 'test_pos_mobile_fix.php'])) {
        continue;
    }
    
    $content = file_get_contents($file);
    if (strpos($content, 'IDarticle') !== false && strpos($content, 'IDarticles') === false) {
        $suspiciousFiles[] = $file;
    }
}

if (!empty($suspiciousFiles)) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🚨 Autres fichiers suspects trouvés:</h3>";
    echo "<ul>";
    foreach ($suspiciousFiles as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
    echo "<p><strong>Action recommandée:</strong> Vérifiez ces fichiers manuellement.</p>";
    echo "</div>";
} else {
    echo "✅ Aucun autre fichier suspect trouvé<br>";
}

echo "<h2>3. Test de pos_mobile.php isolé</h2>";

echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
echo "<h3>✅ Fichiers problématiques isolés</h3>";
echo "<p>Maintenant que les fichiers problématiques sont renommés, testez pos_mobile.php:</p>";
echo "<ul>";
echo "<li><a href='pos_mobile.php?debug=1&v=" . time() . "' target='_blank'>pos_mobile.php (mode debug)</a></li>";
echo "<li><a href='pos_mobile.php?v=" . time() . "' target='_blank'>pos_mobile.php (normal)</a></li>";
echo "</ul>";
echo "</div>";

echo "<h2>4. Comment restaurer les fichiers</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;'>";
echo "<h3>📋 Pour restaurer les fichiers plus tard:</h3>";
echo "<p>Si vous voulez restaurer les fichiers (après avoir confirmé que le problème est résolu):</p>";
echo "<ul>";
foreach ($problematicFiles as $original => $disabled) {
    echo "<li>Renommez $disabled → $original</li>";
}
echo "</ul>";
echo "<p><strong>Ou utilisez ce script de restauration:</strong></p>";
echo "<pre>";
echo "<?php\n";
foreach ($problematicFiles as $original => $disabled) {
    echo "if (file_exists('$disabled')) rename('$disabled', '$original');\n";
}
echo "echo 'Fichiers restaurés';\n";
echo "?>";
echo "</pre>";
echo "</div>";

echo "<h2>5. Vérification finale</h2>";

// Test rapide de connexion
try {
    require_once 'pos_config.php';
    if ($pos->isConnected()) {
        echo "✅ pos_config.php fonctionne correctement<br>";
        
        $articles = $pos->getArticlesByCategory();
        echo "✅ getArticlesByCategory() fonctionne: " . count($articles) . " articles<br>";
        
    } else {
        echo "❌ Problème de connexion POS<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur lors du test: " . $e->getMessage() . "<br>";
}

echo "<h2>✅ Isolation terminée</h2>";
echo "<p><strong>Prochaine étape:</strong> Testez pos_mobile.php avec les liens ci-dessus.</p>";
?>
