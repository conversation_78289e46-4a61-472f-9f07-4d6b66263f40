<?php
/**
 * Script pour vider complètement le cache et les sessions
 * qui pourraient causer la persistance de l'erreur SQL
 */

echo "<h1>🧹 Nettoyage Cache et Sessions</h1>";

// 1. Vider la session
echo "<h2>1. Nettoyage des sessions</h2>";
session_start();
$_SESSION = array();
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}
session_destroy();
echo "✅ Session vidée et détruite<br>";

// 2. Vider les fichiers de cache temporaires
echo "<h2>2. Nettoyage des fichiers temporaires</h2>";
$tempFiles = [
    'articles_success.json',
    'debug_output.txt',
    'error_log.txt'
];

foreach ($tempFiles as $file) {
    if (file_exists($file)) {
        unlink($file);
        echo "✅ Fichier supprimé: $file<br>";
    }
}

// 3. Forcer la recompilation PHP (si opcache est activé)
echo "<h2>3. Nettoyage OPCache PHP</h2>";
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "✅ OPCache PHP vidé<br>";
} else {
    echo "ℹ️ OPCache non disponible<br>";
}

// 4. Test de connexion propre
echo "<h2>4. Test de connexion propre</h2>";
try {
    $pdo = new PDO('odbc:DataCafe', 'admin', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "✅ Connexion PDO propre réussie<br>";
    
    // Test simple
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM articles");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "✅ Test requête: " . $result['total'] . " articles trouvés<br>";
    
} catch (Exception $e) {
    echo "❌ Erreur connexion: " . $e->getMessage() . "<br>";
}

// 5. Instructions pour le navigateur
echo "<h2>5. Instructions pour le navigateur</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<h3>⚠️ Actions à effectuer dans votre navigateur:</h3>";
echo "<ol>";
echo "<li><strong>Vider le cache complet:</strong>";
echo "<ul>";
echo "<li><strong>Chrome:</strong> Ctrl + Shift + Delete → Tout sélectionner → Effacer</li>";
echo "<li><strong>Firefox:</strong> Ctrl + Shift + Delete → Tout sélectionner → Effacer</li>";
echo "<li><strong>Edge:</strong> Ctrl + Shift + Delete → Tout sélectionner → Effacer</li>";
echo "</ul></li>";
echo "<li><strong>Fermer complètement le navigateur</strong></li>";
echo "<li><strong>Rouvrir le navigateur</strong></li>";
echo "<li><strong>Tester pos_mobile.php</strong></li>";
echo "</ol>";
echo "</div>";

// 6. Liens de test avec cache forcé
echo "<h2>6. Liens de test (cache forcé)</h2>";
$timestamp = time();
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
echo "<h3>🔗 Liens avec cache forcé:</h3>";
echo "<ul>";
echo "<li><a href='debug_pos_mobile_error.php?v=$timestamp' target='_blank'>Debug pos_mobile (capture erreurs)</a></li>";
echo "<li><a href='pos_mobile.php?debug=1&v=$timestamp' target='_blank'>pos_mobile.php (mode debug)</a></li>";
echo "<li><a href='pos_mobile.php?v=$timestamp' target='_blank'>pos_mobile.php (normal)</a></li>";
echo "<li><a href='test_pos_mobile_fix.php?v=$timestamp' target='_blank'>Test des méthodes POS</a></li>";
echo "</ul>";
echo "</div>";

// 7. Vérification des fichiers problématiques
echo "<h2>7. Vérification des fichiers problématiques</h2>";
$problematicFiles = [
    'diagnostic_complet.php',
    'test_direct.php'
];

foreach ($problematicFiles as $file) {
    if (file_exists($file)) {
        echo "⚠️ Fichier problématique présent: $file<br>";
        echo "   → Ce fichier contient des requêtes avec 'IDarticle' incorrect<br>";
        echo "   → <strong>Recommandation:</strong> Renommez-le temporairement<br>";
    } else {
        echo "✅ Fichier problématique absent: $file<br>";
    }
}

// 8. Recommandations finales
echo "<h2>8. Plan d'action recommandé</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;'>";
echo "<h3>📋 Étapes à suivre dans l'ordre:</h3>";
echo "<ol>";
echo "<li><strong>Renommez temporairement</strong> les fichiers problématiques:";
echo "<ul><li>diagnostic_complet.php → diagnostic_complet.php.bak</li>";
echo "<li>test_direct.php → test_direct.php.bak</li></ul></li>";
echo "<li><strong>Videz le cache navigateur</strong> (voir instructions ci-dessus)</li>";
echo "<li><strong>Fermez et rouvrez</strong> votre navigateur</li>";
echo "<li><strong>Testez</strong> avec le lien debug ci-dessus</li>";
echo "<li><strong>Si ça marche,</strong> testez pos_mobile.php normal</li>";
echo "</ol>";
echo "</div>";

echo "<h2>✅ Nettoyage terminé</h2>";
echo "<p>Le cache et les sessions ont été vidés. Suivez maintenant les instructions ci-dessus.</p>";
?>
