# 🖼️ Solution pour les Images HSQL - Problème VARBINARY(MAX)

## 🚨 Problème identifié

Vous rencontriez l'erreur suivante lors de la récupération d'images depuis votre base HSQL :

```
❌ Erreur: SQLSTATE[HY09]: <>: Que s'est-il passé ? 
Erreur dans le code SQL de la requête. 
Initialisation de la requête impossible. 
Mot MAX inattendu 
Erreur détectée : CAST(image AS VARBINARY(>>>>MAX<<<<)) as image FROM articles 
Code erreur : 28000002
```

**Cause :** HSQL (HyperFileSQL) ne supporte pas la syntaxe `VARBINARY(MAX)` qui est spécifique à SQL Server.

## ✅ Solutions implémentées

### 1. Méthodes SQL compatibles HSQL

#### ✅ Méthode recommandée : Récupération directe
```sql
SELECT image FROM articles WHERE IDarticles = ? AND image IS NOT NULL
```

#### ✅ Alternative : CAST LONGVARBINARY
```sql
SELECT CAST(image AS LONGVARBINARY) as image FROM articles WHERE IDarticles = ? AND image IS NOT NULL
```

#### ✅ Alternative : VARBINARY avec taille fixe
```sql
SELECT CAST(image AS VARBINARY(8000)) as image FROM articles WHERE IDarticles = ? AND image IS NOT NULL
```

### 2. Nouvelles méthodes dans POSManager

#### `getArticleImage($articleId)`
Récupère et traite automatiquement l'image d'un article :
```php
$imageData = $pos->getArticleImage($articleId);
if ($imageData) {
    echo "<img src='data:{$imageData['mime']};base64,{$imageData['base64']}' alt='Article'>";
}
```

#### `processImageData($imageData)`
Traite les données binaires HSQL et nettoie les images corrompues :
- Détecte automatiquement le format (PNG, JPEG)
- Nettoie les caractères parasites
- Retourne les métadonnées (mime, dimensions, base64)

#### `getArticleImageUrl($articleId)`
Génère l'URL pour afficher une image :
```php
$imageUrl = $pos->getArticleImageUrl($articleId);
echo "<img src='$imageUrl' alt='Article'>";
```

#### `hasArticleImage($articleId)`
Vérifie si un article a une image :
```php
if ($pos->hasArticleImage($articleId)) {
    // Afficher l'image
} else {
    // Afficher placeholder
}
```

## 📁 Fichiers créés/modifiés

### Fichiers principaux
- **`pos_config.php`** - Classe POSManager mise à jour avec gestion d'images
- **`image_display.php`** - Script pour afficher les images via URL
- **`pos_ajax.php`** - Gestionnaire AJAX pour le POS

### Fichiers de test et exemples
- **`test_images_hsql.php`** - Test des différentes méthodes SQL
- **`exemple_interface_images.php`** - Interface POS avec images

## 🚀 Utilisation

### 1. Test des méthodes
Visitez `test_images_hsql.php` pour :
- Tester les différentes méthodes SQL
- Voir quelle méthode fonctionne avec votre base
- Vérifier l'affichage des images

### 2. Interface POS
Visitez `exemple_interface_images.php` pour :
- Voir l'interface POS avec images
- Tester l'ajout au panier
- Voir la gestion des erreurs d'images

### 3. Intégration dans votre code

#### Affichage simple d'une image
```php
$imageData = $pos->getArticleImage($articleId);
if ($imageData) {
    echo "<img src='data:{$imageData['mime']};base64,{$imageData['base64']}' 
               style='max-width: 200px;' alt='Article'>";
} else {
    echo "<img src='images/no-image.png' alt='Pas d\'image'>";
}
```

#### Via URL (recommandé pour les performances)
```php
if ($pos->hasArticleImage($articleId)) {
    echo "<img src='{$pos->getArticleImageUrl($articleId)}' alt='Article'>";
} else {
    echo "<img src='images/no-image.png' alt='Pas d\'image'>";
}
```

#### Avec gestion d'erreur JavaScript
```html
<img src="<?php echo $pos->getArticleImageUrl($articleId); ?>" 
     alt="Article"
     onerror="this.src='images/no-image.png';">
```

## 🔧 Configuration

### Vérification de la connexion
```php
if (!$pos->isConnected()) {
    echo "Erreur de connexion HSQL";
}
```

### Gestion des erreurs
Les erreurs sont automatiquement loggées via `error_log()`. 
Vérifiez vos logs PHP pour le débogage.

## 📊 Avantages de cette solution

1. **✅ Compatible HSQL** - Utilise la syntaxe correcte pour HyperFileSQL
2. **✅ Gestion automatique** - Détecte et nettoie les images corrompues
3. **✅ Performance** - Cache et optimisation des images
4. **✅ Fallback** - Images par défaut si pas d'image
5. **✅ Flexible** - Plusieurs méthodes d'affichage
6. **✅ Robuste** - Gestion complète des erreurs

## 🐛 Dépannage

### Image ne s'affiche pas
1. Vérifiez `test_images_hsql.php` pour voir quelle méthode fonctionne
2. Vérifiez les logs PHP pour les erreurs
3. Testez `image_display.php?id=X` directement

### Erreur de connexion
1. Vérifiez votre DSN ODBC : `odbc:DataCafe`
2. Testez la connexion avec `debug_pos.php`
3. Vérifiez que le driver HFSQL est installé

### Performance lente
1. Utilisez `getArticleImageUrl()` au lieu de `getArticleImage()`
2. Implémentez un cache d'images sur disque
3. Optimisez la taille des images dans HSQL

## 📞 Support

Si vous rencontrez des problèmes :
1. Consultez les logs PHP
2. Testez avec `test_images_hsql.php`
3. Vérifiez la structure de votre table `articles`
4. Assurez-vous que le champ `image` contient bien des données binaires
