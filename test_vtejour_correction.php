<?php
/**
 * Test pour vérifier que les corrections VteJour sont correctes
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Test Corrections VteJour</h1>";

try {
    $pdo = new PDO('odbc:DataCafe', 'admin', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "✅ Connexion réussie<br>";
} catch (Exception $e) {
    echo "❌ Erreur connexion: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>1. Vérification des structures de tables</h2>";

// Structure VteJour
echo "<h3>Table VteJour:</h3>";
try {
    $sql = "SELECT TOP 1 * FROM VteJour";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        $columns = array_keys($result);
        echo "Colonnes: " . implode(', ', $columns) . "<br>";
        
        if (in_array('IDarticle', $columns)) {
            echo "✅ VteJour utilise 'IDarticle' (sans s)<br>";
        }
        if (in_array('IDarticles', $columns)) {
            echo "⚠️ VteJour utilise aussi 'IDarticles' (avec s)<br>";
        }
    } else {
        echo "⚠️ Table VteJour vide<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur VteJour: " . $e->getMessage() . "<br>";
}

// Structure articles
echo "<h3>Table articles:</h3>";
try {
    $sql = "SELECT TOP 1 * FROM articles";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        $columns = array_keys($result);
        if (in_array('IDarticles', $columns)) {
            echo "✅ articles utilise 'IDarticles' (avec s)<br>";
        }
        if (in_array('IDarticle', $columns)) {
            echo "⚠️ articles utilise aussi 'IDarticle' (sans s)<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erreur articles: " . $e->getMessage() . "<br>";
}

echo "<h2>2. Test des requêtes corrigées</h2>";

// Test 1: Requête INSERT dans VteJour (simulée)
echo "<h3>2.1. Test requête INSERT VteJour</h3>";
try {
    // Récupérer un article pour test
    $sql = "SELECT TOP 1 IDarticles, designation, prix, IDCategorie FROM articles";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $article = $stmt->fetch();
    
    if ($article) {
        echo "Article de test: {$article['designation']} (ID: {$article['IDarticles']})<br>";
        
        // Tester la structure de la requête INSERT (sans l'exécuter)
        $sql = "INSERT INTO VteJour (
            articles,
            quantite,
            total,
            IDarticle,
            NumCategories,
            Cuisine,
            IDtickets
        ) VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($sql);
        echo "✅ Requête INSERT préparée avec succès (utilise IDarticle)<br>";
        
        // Test avec des données fictives
        $testData = [
            $article['designation'],    // articles
            1,                         // quantite
            $article['prix'],          // total
            $article['IDarticles'],    // IDarticle (valeur de articles.IDarticles)
            $article['IDCategorie'],   // NumCategories
            '',                        // Cuisine
            999999                     // IDtickets (fictif)
        ];
        
        echo "Données de test préparées<br>";
        
    } else {
        echo "❌ Aucun article trouvé pour test<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur test INSERT: " . $e->getMessage() . "<br>";
}

// Test 2: Requête de jointure
echo "<h3>2.2. Test requête de jointure</h3>";
try {
    $sql = "SELECT TOP 1 v.*, a.designation 
            FROM VteJour v 
            INNER JOIN articles a ON v.IDarticle = a.IDarticles";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ Requête de jointure fonctionne<br>";
        echo "Article trouvé: {$result['designation']}<br>";
    } else {
        echo "⚠️ Requête de jointure ne retourne aucun résultat (normal si VteJour est vide)<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur jointure: " . $e->getMessage() . "<br>";
}

echo "<h2>3. Test avec pos_config.php</h2>";

try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    require_once 'pos_config.php';
    echo "✅ pos_config.php chargé<br>";
    
    if ($pos->isConnected()) {
        echo "✅ POS connecté<br>";
        
        // Test simple d'ajout au panier
        $articles = $pos->getArticlesByCategory();
        if (!empty($articles)) {
            $testArticle = $articles[0];
            $pos->clearCart();
            $result = $pos->addToCart($testArticle['IDarticles'], 1);
            
            if ($result === true || (is_array($result) && $result['success'])) {
                echo "✅ Article ajouté au panier<br>";
                
                // Test de processOrder
                echo "Test de processOrder...<br>";
                $orderId = $pos->processOrder('cash');
                
                if ($orderId) {
                    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
                    echo "🎉 <strong>SUCCÈS TOTAL !</strong><br>";
                    echo "Commande validée avec ID: $orderId<br>";
                    echo "Les corrections VteJour fonctionnent parfaitement !";
                    echo "</div>";
                } else {
                    echo "❌ Échec de processOrder<br>";
                }
            } else {
                echo "❌ Impossible d'ajouter au panier<br>";
            }
        } else {
            echo "❌ Aucun article disponible<br>";
        }
    } else {
        echo "❌ POS non connecté<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur test POS: " . $e->getMessage() . "<br>";
    echo "Fichier: " . $e->getFile() . "<br>";
    echo "Ligne: " . $e->getLine() . "<br>";
}

echo "<h2>4. Résumé des corrections</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;'>";
echo "<h3>📋 Corrections appliquées:</h3>";
echo "<ol>";
echo "<li><strong>INSERT VteJour:</strong> Utilise maintenant 'IDarticle' (sans s) - correspond à la structure de la table</li>";
echo "<li><strong>Jointure VteJour-articles:</strong> v.IDarticle = a.IDarticles - jointure correcte entre les deux tables</li>";
echo "</ol>";

echo "<h3>🎯 Logique:</h3>";
echo "<ul>";
echo "<li>Table <strong>VteJour</strong> utilise <code>IDarticle</code> (sans s)</li>";
echo "<li>Table <strong>articles</strong> utilise <code>IDarticles</code> (avec s)</li>";
echo "<li>La jointure doit donc être: <code>v.IDarticle = a.IDarticles</code></li>";
echo "</ul>";
echo "</div>";

echo "<h2>✅ Test terminé</h2>";
echo "<p>Si le test ci-dessus montre 'SUCCÈS TOTAL', alors pos_mobile.php devrait maintenant fonctionner !</p>";

$timestamp = time();
echo "<p><a href='pos_mobile.php?v=$timestamp' target='_blank'>🔗 Tester pos_mobile.php maintenant</a></p>";
?>
