<?php
require_once 'pos_config.php';

// Gestion des actions admin
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_prices':
            $prices = [];
            foreach ($_POST['prices'] as $articleId => $price) {
                if (is_numeric($price) && $price >= 0) {
                    $prices[$articleId] = floatval($price);
                }
            }
            
            if (file_put_contents('prices.json', json_encode($prices, JSON_PRETTY_PRINT))) {
                $success_message = "Prix mis à jour avec succès !";
            } else {
                $error_message = "Erreur lors de la sauvegarde des prix.";
            }
            break;
            
        case 'clear_orders':
            $date = $_POST['date'] ?? date('Y-m-d');
            $ordersFile = 'orders/' . $date . '.json';
            
            if (file_exists($ordersFile)) {
                unlink($ordersFile);
                $success_message = "Commandes du $date supprimées.";
            }
            break;
    }
}

// Récupération des données
$categories = $pos->getCategories();
$articles = $pos->getArticlesByCategory();
$todayStats = $pos->getTodayStatsFromDB();

// Chargement des prix actuels
$currentPrices = [];
if (file_exists('prices.json')) {
    $currentPrices = json_decode(file_get_contents('prices.json'), true) ?? [];
}

// Statistiques des derniers jours depuis la base de données
$weekStats = $pos->getWeekStatsFromDB();

// Statistiques détaillées de la semaine
$startDate = date('Y-m-d', strtotime('-7 days'));
$endDate = date('Y-m-d');
$detailedStats = $pos->getDetailedStatsFromDB($startDate, $endDate);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo POS_NAME; ?> - Administration</title>
    
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-link {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-link:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-icon {
            font-size: 40px;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .price-input {
            width: 80px;
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: right;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                flex-direction: column;
                gap: 10px;
            }
        }



        /* Styles pour les statistiques détaillées */
        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 10px;
        }

        .summary-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
        }

        .summary-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .summary-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .summary-card:nth-child(1) .summary-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .summary-card:nth-child(2) .summary-icon {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .summary-card:nth-child(3) .summary-icon {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .summary-card:nth-child(4) .summary-icon {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
        }

        .summary-content {
            flex: 1;
        }

        .summary-value {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-chart-bar"></i>
            </div>
            <div class="nav-links">
                <a href="pos_mobile.php" class="nav-link">
                    <i class="fas fa-cash-register"></i> Retour POS
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Messages -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- Dashboard Stats -->
        <div class="dashboard-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-value"><?php echo $todayStats['orders_count']; ?></div>
                <div class="stat-label">Commandes aujourd'hui</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value"><?php echo formatPrice($todayStats['total_revenue']); ?></div>
                <div class="stat-label">CA aujourd'hui</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-value"><?php echo $todayStats['items_sold']; ?></div>
                <div class="stat-label">Articles vendus</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-list"></i>
                </div>
                <div class="stat-value"><?php echo count($articles); ?></div>
                <div class="stat-label">Articles en stock</div>
            </div>
        </div>


        <!-- Statistiques détaillées de la semaine -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-calendar-week"></i> Résumé de la semaine (<?php echo date('d/m', strtotime($startDate)); ?> - <?php echo date('d/m', strtotime($endDate)); ?>)
                </h2>
            </div>
            <div class="section-content">
                <div class="stats-summary">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-value"><?php echo $detailedStats['total_tickets']; ?></div>
                            <div class="summary-label">Tickets total</div>
                        </div>
                    </div>

                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-value"><?php echo formatPrice($detailedStats['total_revenue']); ?></div>
                            <div class="summary-label">Chiffre d'affaires</div>
                        </div>
                    </div>

                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-shopping-basket"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-value"><?php echo $detailedStats['total_items']; ?></div>
                            <div class="summary-label">Articles vendus</div>
                        </div>
                    </div>

                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-value"><?php echo formatPrice($detailedStats['average_ticket']); ?></div>
                            <div class="summary-label">Ticket moyen</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid-2">
            <!-- Graphique des ventes -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-chart-line"></i> Ventes des 7 derniers jours
                    </h2>
                </div>
                <div class="section-content">
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-tools"></i> Actions rapides
                    </h2>
                </div>
                <div class="section-content">
                    <div style="display: flex; flex-direction: column; gap: 15px;">
                                                
                        <button onclick="exportData()" class="btn btn-secondary">
                            <i class="fas fa-download"></i> Exporter les données
                        </button>
                        
                        <form method="post" style="margin: 0;" onsubmit="return confirm('Supprimer toutes les commandes d\'aujourd\'hui ?')">
                            <input type="hidden" name="action" value="clear_orders">
                            <input type="hidden" name="date" value="<?php echo date('Y-m-d'); ?>">
                            <button type="submit" class="btn btn-danger" style="width: 100%;">
                                <i class="fas fa-trash"></i> Vider les commandes du jour
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

     
    <script>
        // Graphique des ventes
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesData = <?php echo json_encode($weekStats); ?>;
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: salesData.map(day => {
                    const date = new Date(day.date);
                    return date.toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' });
                }),
                datasets: [{
                    label: 'Chiffre d\'affaires',
                    data: salesData.map(day => day.revenue),
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Nombre de commandes',
                    data: salesData.map(day => day.orders),
                    borderColor: '#764ba2',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Chiffre d\'affaires'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Nombre de commandes'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
        
        // Fonction d'export
        function exportData() {
            const today = new Date().toISOString().split('T')[0];
            window.open(`pos_export.php?date=${today}`, '_blank');
        }
        
        // Auto-refresh des stats toutes les 5 minutes
        setInterval(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
