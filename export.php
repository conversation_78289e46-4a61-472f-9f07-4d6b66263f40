<?php
/**
 * Export des images HSQL vers fichiers PNG
 * Version reproduisant exactement la logique WinDev fonctionnelle
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300); // 5 minutes max

// Variables de configuration (reproduction exacte du code WinDev)
$sCheminDestination = "D:\\xampp\\htdocs\\Becoffe\\images\\articles"; // Chemin exact du code WinDev
$sNomFichier = "";
$sNomFichierPNG = "";
$nCompteur = 0;

echo "<h1>🚀 Export Images HSQL → PNG (Méthode WinDev)</h1>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h3>📋 Reproduction du code WinDev fonctionnel</h3>";
echo "<p>Cette version PHP reproduit <strong>exactement</strong> la logique de votre code WinDev qui fonctionne à 100%.</p>";
echo "</div>";

// Créer le dossier de destination s'il n'existe pas (équivalent fRep + fRepCrée)
if (!is_dir($sCheminDestination)) {
    if (mkdir($sCheminDestination, 0755, true)) {
        echo "<p style='color: green;'>✅ Dossier créé: {$sCheminDestination}</p>";
    } else {
        echo "<p style='color: red;'>❌ Impossible de créer le dossier: {$sCheminDestination}</p>";
        exit;
    }
} else {
    echo "<p style='color: blue;'>ℹ️ Dossier existe: {$sCheminDestination}</p>";
}

// Connexion à la base de données HSQL
require_once 'pos_config.php';

if (!$pos->isConnected()) {
    echo "<p style='color: red;'>❌ Erreur de connexion à la base HSQL</p>";
    exit;
}

echo "<p style='color: green;'>✅ Connexion HSQL réussie</p>";

// Récupérer tous les articles avec images (équivalent POUR TOUT articles)
try {
    $stmt = $pos->pdo->prepare("SELECT IDarticles, designation, image FROM articles WHERE image IS NOT NULL ORDER BY IDarticles");
    $stmt->execute();
    $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<p><strong>Articles avec images trouvés:</strong> " . count($articles) . "</p>";

    if (empty($articles)) {
        echo "<p style='color: orange;'>⚠️ Aucun article avec image trouvé</p>";
        exit;
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur requête: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

echo "<h2>📤 Export en cours (Méthode WinDev)...</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Parcourir tous les articles (équivalent POUR TOUT articles)
foreach ($articles as $article) {
    $nCompteur++; // Équivalent nCompteur++

    echo "<div style='margin: 5px 0; padding: 5px; background: white; border-radius: 3px;'>";
    echo "<strong>Article {$article['IDarticles']}:</strong> " . htmlspecialchars($article['designation']) . " - ";

    // Nom du fichier temporaire pour l'extraction (équivalent WinDev)
    $sNomFichier = $sCheminDestination . "\\article_" . $article['IDarticles'];

    // Nom final en PNG (équivalent WinDev)
    $sNomFichierPNG = $sCheminDestination . "\\" . $article['IDarticles'] . ".png";

    // Extraire l'image du mémo (équivalent HExtraitMémo)
    if (HExtraitMemo_PHP($article, 'image', $sNomFichier)) {

        // Charger l'image extraite (équivalent dChargeImage)
        $MonImage = dChargeImage_PHP($sNomFichier);

        if ($MonImage !== null) {
            // Sauvegarder au format PNG (équivalent dSauveImagePNG)
            if (dSauveImagePNG_PHP($MonImage, $sNomFichierPNG)) {
                echo "<span style='color: green;'>✅ Exporté (méthode WinDev)</span>";
            } else {
                echo "<span style='color: red;'>❌ Erreur sauvegarde PNG</span>";
            }

            // Supprimer le fichier temporaire (équivalent fSupprime)
            if (file_exists($sNomFichier) && $sNomFichier !== $sNomFichierPNG) {
                unlink($sNomFichier);
            }

            // Libérer la mémoire (équivalent automatique WinDev)
            if (is_resource($MonImage)) {
                imagedestroy($MonImage);
            }
        } else {
            echo "<span style='color: red;'>❌ Impossible de charger l'image extraite</span>";
        }
    } else {
        echo "<span style='color: orange;'>⚠️ Extraction mémo échouée</span>";
    }

    echo "</div>";

    // Forcer l'affichage
    if (ob_get_level()) ob_flush();
    flush();
}

echo "</div>";

// Message de fin (équivalent Info() de WinDev)
echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>✅ Export terminé</h2>";
echo "<p style='font-size: 18px;'><strong>Export terminé : {$nCompteur} articles traités</strong></p>";
echo "<p>Méthode utilisée : <strong>Reproduction exacte du code WinDev fonctionnel</strong></p>";
echo "</div>";

if ($nCompteur > 0) {
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px;'>";
    echo "<h3>� Prochaines étapes :</h3>";
    echo "<ol>";
    echo "<li><strong>Vérifiez le dossier :</strong> <code>images/articles/</code></li>";
    echo "<li><strong>Testez l'affichage :</strong> <a href='verify_exported_images.php' target='_blank'>verify_exported_images.php</a></li>";
    echo "<li><strong>Utilisez dans l'app :</strong> <a href='pos_mobile.php' target='_blank'>pos_mobile.php</a></li>";
    echo "</ol>";
    echo "</div>";
}

/**
 * Équivalent PHP de HExtraitMémo() de WinDev
 * Extrait les données d'un champ mémo vers un fichier
 * @param array $article - L'enregistrement de l'article
 * @param string $champImage - Le nom du champ contenant l'image
 * @param string $cheminFichier - Le chemin du fichier de destination
 * @return bool - True si l'extraction réussit, False sinon
 */
function HExtraitMemo_PHP($article, $champImage, $cheminFichier) {
    if (!isset($article[$champImage]) || empty($article[$champImage])) {
        return false;
    }

    $imageData = $article[$champImage];

    // Si c'est une ressource (stream HSQL), la lire
    if (is_resource($imageData)) {
        $imageData = stream_get_contents($imageData);
    }

    if (empty($imageData)) {
        return false;
    }

    // Écrire les données brutes dans le fichier (comme HExtraitMémo)
    return file_put_contents($cheminFichier, $imageData) !== false;
}

/**
 * Équivalent PHP de dChargeImage() de WinDev avec diagnostic
 * Charge une image depuis un fichier avec réparation automatique
 * @param string $cheminFichier - Le chemin du fichier image
 * @return resource|null - La ressource image ou null en cas d'erreur
 */
function dChargeImage_PHP($cheminFichier) {
    if (!file_exists($cheminFichier)) {
        echo "<span style='color: red;'>Fichier inexistant</span> - ";
        return null;
    }

    $fileSize = filesize($cheminFichier);
    echo "Fichier: {$fileSize} octets - ";

    // Lire le contenu pour diagnostic
    $content = file_get_contents($cheminFichier);
    if (empty($content)) {
        echo "<span style='color: red;'>Fichier vide</span> - ";
        return null;
    }

    // Afficher les premiers octets pour diagnostic
    $hexStart = bin2hex(substr($content, 0, 16));
    echo "Début: {$hexStart} - ";

    // Essayer getimagesize d'abord
    $imageInfo = @getimagesize($cheminFichier);
    if ($imageInfo !== false) {
        echo "Format détecté: " . image_type_to_mime_type($imageInfo[2]) . " - ";

        $image = false;
        switch ($imageInfo[2]) {
            case IMAGETYPE_PNG:
                $image = @imagecreatefrompng($cheminFichier);
                break;
            case IMAGETYPE_JPEG:
                $image = @imagecreatefromjpeg($cheminFichier);
                break;
            case IMAGETYPE_GIF:
                $image = @imagecreatefromgif($cheminFichier);
                break;
            case IMAGETYPE_BMP:
                if (function_exists('imagecreatefrombmp')) {
                    $image = @imagecreatefrombmp($cheminFichier);
                }
                break;
        }

        if ($image !== false) {
            echo "<span style='color: green;'>Chargement standard OK</span> - ";
            return $image;
        } else {
            echo "<span style='color: orange;'>Chargement standard échoué</span> - ";
        }
    } else {
        echo "Format non reconnu - ";
    }

    // Si échec, essayer de réparer les données
    echo "Tentative réparation - ";

    // Chercher signature PNG
    $pngSignature = "\x89PNG\r\n\x1a\n";
    $pngPos = strpos($content, $pngSignature);

    if ($pngPos !== false) {
        echo "PNG trouvé pos {$pngPos} - ";
        $cleanData = substr($content, $pngPos);

        // Nettoyer les données PNG
        $cleanData = rtrim($cleanData, "\x00");

        // Vérifier la fin PNG
        $pngEnd = "IEND\xaeB`\x82";
        if (substr($cleanData, -8) !== $pngEnd) {
            $cleanData .= "\x00\x00\x00\x00" . $pngEnd;
        }

        // Sauvegarder la version nettoyée
        $cleanFile = $cheminFichier . '_clean.png';
        if (file_put_contents($cleanFile, $cleanData)) {
            $image = @imagecreatefrompng($cleanFile);
            if ($image !== false) {
                echo "<span style='color: blue;'>Réparation PNG réussie</span> - ";
                // Remplacer le fichier original par la version nettoyée
                copy($cleanFile, $cheminFichier);
                unlink($cleanFile);
                return $image;
            }
            unlink($cleanFile);
        }
    }

    // Chercher signature JPEG
    $jpegPos = strpos($content, "\xFF\xD8");
    if ($jpegPos !== false) {
        echo "JPEG trouvé pos {$jpegPos} - ";
        $cleanData = substr($content, $jpegPos);

        // Chercher la fin JPEG
        $jpegEndPos = strrpos($cleanData, "\xFF\xD9");
        if ($jpegEndPos !== false) {
            $cleanData = substr($cleanData, 0, $jpegEndPos + 2);
        }

        // Sauvegarder la version nettoyée
        $cleanFile = $cheminFichier . '_clean.jpg';
        if (file_put_contents($cleanFile, $cleanData)) {
            $image = @imagecreatefromjpeg($cleanFile);
            if ($image !== false) {
                echo "<span style='color: blue;'>Réparation JPEG réussie</span> - ";
                // Remplacer le fichier original par la version nettoyée
                copy($cleanFile, $cheminFichier);
                unlink($cleanFile);
                return $image;
            }
            unlink($cleanFile);
        }
    }

    echo "<span style='color: red;'>Toutes réparations échouées</span> - ";
    return null;
}

/**
 * Équivalent PHP de dSauveImagePNG() de WinDev
 * Sauvegarde une image au format PNG
 * @param resource $image - La ressource image
 * @param string $cheminFichier - Le chemin du fichier de destination
 * @return bool - True si la sauvegarde réussit, False sinon
 */
function dSauveImagePNG_PHP($image, $cheminFichier) {
    if (!is_resource($image) && !($image instanceof GdImage)) {
        return false;
    }

    // Sauvegarder au format PNG avec compression optimale (comme dSauveImagePNG)
    return imagepng($image, $cheminFichier, 9);
}

// Fin des fonctions équivalentes WinDev

// Test d'affichage des images exportées (méthode WinDev)
echo "<h2>🧪 Test des images exportées</h2>";

if ($nCompteur > 0) {
    echo "<p>Aperçu des images exportées avec la méthode WinDev :</p>";

    // Utiliser le chemin Windows exact du code WinDev
    $testFiles = glob($sCheminDestination . "\\*.png");
    if (empty($testFiles)) {
        // Fallback avec séparateur Unix
        $testFiles = glob($sCheminDestination . "/*.png");
    }

    $testFiles = array_slice($testFiles, 0, 6);

    if (!empty($testFiles)) {
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;'>";

        foreach ($testFiles as $file) {
            $filename = basename($file);
            $articleId = pathinfo($filename, PATHINFO_FILENAME);

            echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 10px; text-align: center; background: white;'>";
            echo "<img src='images/articles/{$filename}' style='width: 100%; max-width: 120px; height: 80px; object-fit: cover; border-radius: 5px;'
                       onload=\"this.nextElementSibling.innerHTML='✅ WinDev OK'; this.nextElementSibling.style.color='green';\"
                       onerror=\"this.nextElementSibling.innerHTML='❌ Erreur'; this.nextElementSibling.style.color='red';\"
                       alt='Article {$articleId}'>";
            echo "<div style='margin-top: 5px; font-weight: bold;'>⏳ Test...</div>";
            echo "<p style='margin: 5px 0; font-size: 12px;'><strong>ID:</strong> {$articleId}</p>";
            echo "</div>";
        }

        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠️ Aucun fichier PNG trouvé dans {$sCheminDestination}</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ Aucun article traité</p>";
}

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3; margin-top: 30px;'>";
echo "<h3>💡 Informations importantes</h3>";
echo "<ul>";
echo "<li><strong>Dossier de destination :</strong> <code>images/articles/</code></li>";
echo "<li><strong>Format des noms :</strong> <code>IDarticle.png</code> (ex: 123.png)</li>";
echo "<li><strong>Format de sortie :</strong> PNG optimisé</li>";
echo "<li><strong>Réparation automatique :</strong> Images corrompues nettoyées avec GD</li>";
echo "</ul>";
echo "</div>";

?>

<script>
console.log('🚀 Export images HSQL terminé');

document.addEventListener('DOMContentLoaded', function() {
    const exportedImages = document.querySelectorAll('img[src*="images/articles/"]');
    let loadedCount = 0;

    console.log(`📊 Images exportées à tester: ${exportedImages.length}`);

    exportedImages.forEach((img, index) => {
        img.addEventListener('load', function() {
            loadedCount++;
            console.log(`✅ Image exportée ${index + 1} chargée:`, this.src);
        });

        img.addEventListener('error', function() {
            console.log(`❌ Erreur image exportée ${index + 1}:`, this.src);
        });
    });

    setTimeout(() => {
        console.log(`📊 Images chargées: ${loadedCount}/${exportedImages.length}`);
        if (loadedCount === exportedImages.length && exportedImages.length > 0) {
            console.log('🎉 SUCCÈS: Toutes les images exportées fonctionnent !');
        }
    }, 3000);
});
</script>

?>