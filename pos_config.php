<?php
/**
 * Configuration POS - Système de Point de Vente
 * Basé sur votre solution HFSQL qui fonctionne
 */

// La session doit être démarrée dans le fichier appelant

// Configuration de la base de données (votre solution qui marche)
define('POS_DSN', 'odbc:DataCafe');
define('POS_USERNAME', 'admin');
define('POS_PASSWORD', '');

// Configuration du POS
define('POS_NAME', 'BeCoffe POS');
define('POS_VERSION', '1.0');
define('POS_CURRENCY', '');
define('POS_TAX_RATE', 0.20); // 20% TVA

// Configuration des sessions
session_start();

/**
 * Classe POS Manager - Gestion complète du point de vente
 */
class POSManager {
    public $pdo;  // Changé en public pour les tests
    private $connected = false;
    
    public function __construct() {
        $this->connect();
    }
    
    private function connect() {
        try {
            $this->pdo = new PDO(POS_DSN, POS_USERNAME, POS_PASSWORD, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 30
            ]);
            $this->connected = true;
        } catch (PDOException $e) {
            $this->connected = false;
            error_log("Erreur connexion POS: " . $e->getMessage());
        }
    }
    
    public function isConnected() {
        return $this->connected;
    }
    
    /**
     * Récupérer toutes les catégories pour le menu
     */
    public function getCategories() {
        if (!$this->connected) return [];
        
        try {
            $sql = "SELECT IDCategorie, categories, photo FROM Categorie ORDER BY categories";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Erreur getCategories: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Récupérer les articles d'une catégorie
     */
    public function getArticlesByCategory($categoryId = null, $includeZeroStock = true) {
        if (!$this->connected) return [];

        try {
            $stockCondition = $includeZeroStock ? "" : "AND a.quantite > 0";

            if ($categoryId) {
                $sql = "SELECT a.IDarticles, a.designation, a.quantite, a.IDCategorie, a.prix,
                               CASE WHEN a.image IS NOT NULL THEN 1 ELSE 0 END as has_image,
                               c.categories as nom_categorie
                        FROM articles a
                        LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                        WHERE a.IDCategorie = ? $stockCondition
                        ORDER BY a.designation";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute([$categoryId]);
            } else {
                $sql = "SELECT a.IDarticles, a.designation, a.quantite, a.IDCategorie, a.prix,
                               CASE WHEN a.image IS NOT NULL THEN 1 ELSE 0 END as has_image,
                               c.categories as nom_categorie
                        FROM articles a
                        LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                        WHERE 1=1 $stockCondition
                        ORDER BY c.categories, a.designation";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute();
            }
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Erreur getArticlesByCategory: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Récupérer l'image d'un article avec gestion HSQL
     * Utilise la méthode directe qui fonctionne avec HSQL
     */
    public function getArticleImage($articleId) {
        if (!$this->connected) return null;

        try {
            // Méthode directe - fonctionne avec HSQL
            $sql = "SELECT image FROM articles WHERE IDarticles = ? AND image IS NOT NULL";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$articleId]);
            $result = $stmt->fetch();

            if ($result && !empty($result['image'])) {
                return $this->processImageData($result['image']);
            }

            return null;
        } catch (Exception $e) {
            error_log("Erreur getArticleImage: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Traiter les données d'image récupérées depuis HSQL
     */
    private function processImageData($imageData) {
        if (empty($imageData)) return null;

        // Si c'est une ressource (stream), la lire
        if (is_resource($imageData)) {
            $imageData = stream_get_contents($imageData);
        }

        // CORRECTION SPÉCIALE POUR PNG CORROMPUS HSQL
        // Vérifier si c'est un PNG avec signature correcte mais fin corrompue
        $pngSignature = "\x89PNG\r\n\x1a\n";
        if (substr($imageData, 0, 8) === $pngSignature) {
            // C'est un PNG, mais probablement corrompu à la fin
            // Essayer de le réparer avec GD
            if (function_exists('imagecreatefromstring')) {
                // Forcer GD à lire le PNG même s'il est corrompu
                $gdImage = @imagecreatefromstring($imageData);
                if ($gdImage) {
                    // Recréer un PNG propre
                    ob_start();
                    imagepng($gdImage, null, 9); // Compression maximale
                    $repairedData = ob_get_contents();
                    ob_end_clean();
                    imagedestroy($gdImage);

                    if (!empty($repairedData)) {
                        $imageInfo = @getimagesizefromstring($repairedData);
                        if ($imageInfo) {
                            return [
                                'data' => $repairedData,
                                'mime' => 'image/png',
                                'width' => $imageInfo[0],
                                'height' => $imageInfo[1],
                                'base64' => base64_encode($repairedData)
                            ];
                        }
                    }
                }

                // Si GD échoue, essayer de réparer manuellement
                // Supprimer les zéros à la fin et ajouter la bonne signature de fin
                $imageData = rtrim($imageData, "\x00"); // Supprimer les zéros à la fin
                $pngEnd = "IEND\xaeB`\x82";

                // Vérifier si la fin PNG est déjà présente
                if (substr($imageData, -8) !== $pngEnd) {
                    // Chercher le dernier chunk valide et ajouter IEND
                    $imageData .= "\x00\x00\x00\x00" . $pngEnd;
                }

                // Tester l'image réparée
                $imageInfo = @getimagesizefromstring($imageData);
                if ($imageInfo) {
                    return [
                        'data' => $imageData,
                        'mime' => 'image/png',
                        'width' => $imageInfo[0],
                        'height' => $imageInfo[1],
                        'base64' => base64_encode($imageData)
                    ];
                }
            }
        }

        // Vérifier si c'est une image valide directement (pour les autres formats)
        $imageInfo = @getimagesizefromstring($imageData);
        if ($imageInfo) {
            return [
                'data' => $imageData,
                'mime' => $imageInfo['mime'],
                'width' => $imageInfo[0],
                'height' => $imageInfo[1],
                'base64' => base64_encode($imageData)
            ];
        }

        // Essayer de nettoyer l'image (supprimer les caractères parasites)
        // Chercher signature PNG complète
        $pngSignature = "\x89PNG\r\n\x1a\n";
        $pngPos = strpos($imageData, $pngSignature);
        if ($pngPos !== false) {
            $cleanData = substr($imageData, $pngPos);
            $imageInfo = @getimagesizefromstring($cleanData);
            if ($imageInfo) {
                // Nettoyer avec GD si possible
                if (function_exists('imagecreatefromstring')) {
                    $gdImage = @imagecreatefromstring($cleanData);
                    if ($gdImage) {
                        ob_start();
                        imagepng($gdImage);
                        $finalData = ob_get_contents();
                        ob_end_clean();
                        imagedestroy($gdImage);

                        if (!empty($finalData)) {
                            $cleanData = $finalData;
                            $imageInfo = @getimagesizefromstring($cleanData);
                        }
                    }
                }

                return [
                    'data' => $cleanData,
                    'mime' => $imageInfo['mime'],
                    'width' => $imageInfo[0],
                    'height' => $imageInfo[1],
                    'base64' => base64_encode($cleanData)
                ];
            }
        }

        // Chercher signature JPEG
        $jpegPos = strpos($imageData, "\xFF\xD8");
        if ($jpegPos !== false) {
            $cleanData = substr($imageData, $jpegPos);
            $imageInfo = @getimagesizefromstring($cleanData);
            if ($imageInfo) {
                // Nettoyer avec GD si possible
                if (function_exists('imagecreatefromstring')) {
                    $gdImage = @imagecreatefromstring($cleanData);
                    if ($gdImage) {
                        ob_start();
                        imagejpeg($gdImage, null, 90);
                        $finalData = ob_get_contents();
                        ob_end_clean();
                        imagedestroy($gdImage);

                        if (!empty($finalData)) {
                            $cleanData = $finalData;
                            $imageInfo = @getimagesizefromstring($cleanData);
                        }
                    }
                }

                return [
                    'data' => $cleanData,
                    'mime' => $imageInfo['mime'],
                    'width' => $imageInfo[0],
                    'height' => $imageInfo[1],
                    'base64' => base64_encode($cleanData)
                ];
            }
        }

        return null;
    }
    
    /**
     * Rechercher des articles
     */
   public function searchArticles($search, $includeZeroStock = true) {
    if (!$this->connected) return [];

    try {
        $stockCondition = $includeZeroStock ? "" : "AND a.quantite > 0";

        $sql = "SELECT a.IDarticles, a.designation, a.quantite, a.IDCategorie, a.prix,
                       CASE WHEN a.image IS NOT NULL THEN 1 ELSE 0 END as has_image,
                       c.categories as nom_categorie
                FROM articles a
                LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                WHERE a.designation LIKE ? $stockCondition
                ORDER BY a.designation";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['%' . $search . '%']);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Erreur searchArticles: " . $e->getMessage());
        return [];
    }
}

    /**
     * Obtenir l'URL de l'image d'un article
     */
    public function getArticleImageUrl($articleId) {
        return "image_display.php?id=" . intval($articleId);
    }

    /**
     * Vérifier si un article a une image
     */
    public function hasArticleImage($articleId) {
        if (!$this->connected) return false;

        try {
            $sql = "SELECT COUNT(*) as count FROM articles WHERE IDarticles = ? AND image IS NOT NULL";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$articleId]);
            $result = $stmt->fetch();

            return $result && $result['count'] > 0;
        } catch (Exception $e) {
            error_log("Erreur hasArticleImage: " . $e->getMessage());
            return false;
        }
    }
    /**
     * Gestion du panier de commande
     */
    public function addToCart($articleId, $quantity = 1, $allowInsufficientStock = true) {
        if (!isset($_SESSION['pos_cart'])) {
            $_SESSION['pos_cart'] = [];
        }

        // Vérifier si l'article existe
        $article = $this->getArticleById($articleId);
        if (!$article) {
            return ['success' => false, 'message' => 'Article introuvable'];
        }

        // Vérifier le stock seulement si la validation est activée
        if (!$allowInsufficientStock && $article['quantite'] < $quantity) {
            return ['success' => false, 'message' => 'Stock insuffisant'];
        }

        // Déterminer si le stock est insuffisant pour l'affichage
        $isStockInsufficient = $article['quantite'] < $quantity;

        // Ajouter au panier
        if (isset($_SESSION['pos_cart'][$articleId])) {
            $_SESSION['pos_cart'][$articleId]['quantity'] += $quantity;
        } else {
            $_SESSION['pos_cart'][$articleId] = [
                'article' => $article,
                'quantity' => $quantity,
                'price' => $this->getArticlePrice($articleId),
                'stock_insufficient' => $isStockInsufficient
            ];
        }

        // Retourner le résultat avec information sur le stock
        $message = $isStockInsufficient ? 'Article ajouté (stock insuffisant)' : 'Article ajouté';
        return ['success' => true, 'message' => $message, 'stock_insufficient' => $isStockInsufficient];
    }
    
    /**
     * Récupérer un article par ID
     */
    public function getArticleById($id) {
        if (!$this->connected) return null;
        
        try {
            $sql = "SELECT a.IDarticles, a.designation, a.quantite, a.IDCategorie, a.prix, a.image, c.categories as nom_categorie
                    FROM articles a
                    LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                    WHERE a.IDarticles = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Erreur getArticleById: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Obtenir le prix d'un article
     */
    public function getArticlePrice($articleId) {
        // 1. Récupérer l'article avec son prix de la base de données
        $article = $this->getArticleById($articleId);
        if ($article && isset($article['prix']) && $article['prix'] > 0) {
            return floatval($article['prix']);
        }

        // 2. Chargement des prix depuis un fichier JSON (override)
        $pricesFile = 'prices.json';
        if (file_exists($pricesFile)) {
            $prices = json_decode(file_get_contents($pricesFile), true) ?? [];
            if (isset($prices[$articleId])) {
                return floatval($prices[$articleId]);
            }
        }

        // 3. Prix par défaut basés sur les catégories (fallback)
        if ($article) {
            $defaultPrices = [
                1 => 2.50,  // Boissons chaudes
                2 => 2.00,  // Boissons froides
                3 => 4.50,  // Pâtisseries
                4 => 5.50,  // Sandwichs
                5 => 4.00,  // Salades
            ];

            return $defaultPrices[$article['IDCategorie']] ?? 3.00;
        }

        return 3.00; // Prix par défaut
    }
    
    /**
     * Récupérer le contenu du panier
     */
    public function getCart() {
        return $_SESSION['pos_cart'] ?? [];
    }
    
    /**
     * Calculer le total du panier
     */
    public function getCartTotal() {
        $cart = $this->getCart();
        $total = 0;
        
        foreach ($cart as $item) {
            $total += $item['price'] * $item['quantity'];
        }
        
        return $total;
    }
    
    /**
     * Calculer la TVA
     */
    public function getCartTax() {
        return $this->getCartTotal() * POS_TAX_RATE;
    }
    
    /**
     * Total TTC
     */
    public function getCartTotalTTC() {
        return $this->getCartTotal() + $this->getCartTax();
    }
    
    /**
     * Vider le panier
     */
    public function clearCart() {
        $_SESSION['pos_cart'] = [];
    }
    
    /**
     * Supprimer un article du panier
     */
    public function removeFromCart($articleId) {
        if (isset($_SESSION['pos_cart'][$articleId])) {
            unset($_SESSION['pos_cart'][$articleId]);
            return true;
        }
        return false;
    }
    
    /**
     * Modifier la quantité d'un article dans le panier
     */
    public function updateCartQuantity($articleId, $quantity) {
        if (isset($_SESSION['pos_cart'][$articleId])) {
            if ($quantity <= 0) {
                $this->removeFromCart($articleId);
            } else {
                // Vérifier le stock pour mettre à jour l'indicateur
                $article = $this->getArticleById($articleId);
                $isStockInsufficient = $article && $article['quantite'] < $quantity;

                $_SESSION['pos_cart'][$articleId]['quantity'] = $quantity;
                $_SESSION['pos_cart'][$articleId]['stock_insufficient'] = $isStockInsufficient;
            }
            return true;
        }
        return false;
    }
    
    /**
     * Finaliser la commande selon la procédure WinDev
     */
    public function processOrder($paymentMethod = 'cash', $serverId = null, $tableId = null) {
        $cart = $this->getCart();
        if (empty($cart)) {
            error_log("Erreur processOrder: Panier vide");
            return false;
        }

        // Utiliser les valeurs par défaut si non spécifiées
        if ($serverId === null) {
            $serverId = $this->getCurrentServerId();
        }
        if ($tableId === null) {
            $tableId = $this->getCurrentTableId();
        }

        // Sauvegarder la commande dans les tables tickets et VteJour
        $ticketId = $this->saveOrder($cart, $paymentMethod, $serverId, $tableId);

        if ($ticketId) {
            // Vider le panier après succès
            $this->clearCart();

            // Log de succès
            error_log("Commande validée avec succès - Ticket ID: $ticketId, Serveur: $serverId, Table: $tableId");

            return $ticketId;
        }

        error_log("Erreur processOrder: Échec de la sauvegarde");
        return false;
    }
    
    /**
     * Sauvegarder la commande selon la procédure WinDev
     */
    private function saveOrder($cart, $paymentMethod, $serverId = 1, $tableId = 103) {
        if (!$this->connected) {
            error_log("Erreur saveOrder: Pas de connexion à la base de données");
            return false;
        }

        try {
            // Commencer une transaction
            $this->pdo->beginTransaction();
            error_log("DEBUG: Transaction démarrée");

            // 1. Lire le dernier ticket pour obtenir le prochain NumTick
            $sql = "SELECT MAX(NumTick) as last_num FROM tickets";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch();
            $nextNumTick = ($result['last_num'] ?? 0) + 1;
            error_log("DEBUG: Prochain NumTick: $nextNumTick");

            // 2. Calculer le total du panier
            $total = $this->getCartTotalTTC();
            error_log("DEBUG: Total calculé: $total");

            // 3. Insérer le nouveau ticket
            $dateNum = intval(date('Ymd'));     // Convertir en entier
            $timeNum = intval(date('His'));     // Convertir en entier
            $paymentCode = $this->getPaymentTypeCode($paymentMethod);

            error_log("DEBUG: Données ticket - NumTick: $nextNumTick, Serveur: $serverId, Table: $tableId, Date: $dateNum, Heure: $timeNum, Total: $total, Paiement: $paymentCode");

            // Essayer d'abord une insertion simple
            $sql = "INSERT INTO tickets (NumTick, IDServeur, IDTables, DATE, heure, total, typepaye, Djr, ImpTick) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $this->pdo->prepare($sql);

            // Préparer les données avec les bons types
            $insertData = [
                intval($nextNumTick),   // NumTick - entier
                intval($serverId),      // IDServeur - entier
                intval($tableId),       // IDTables - entier
                $dateNum,               // DATE - entier YYYYMMDD
                $timeNum,               // heure - entier HHMMSS
                floatval($total),       // total - décimal
                $paymentCode,           // typepaye - chaîne
                1,                      // Djr - entier (1 = vrai)
                1                       // ImpTick - entier (1 = vrai)
            ];

            error_log("DEBUG: Données préparées pour insertion: " . print_r($insertData, true));

            $success = $stmt->execute($insertData);

            if (!$success) {
                $errorInfo = $stmt->errorInfo();
                error_log("DEBUG: Erreur SQL ticket - " . print_r($errorInfo, true));
                throw new Exception("Erreur lors de l'insertion du ticket: " . $errorInfo[2]);
            }

            // 4. Récupérer l'ID du ticket inséré
            $ticketId = $this->pdo->lastInsertId();

            // Si lastInsertId ne fonctionne pas avec HFSQL, récupérer par NumTick
            if (!$ticketId) {
                $sql = "SELECT IDtickets FROM tickets WHERE NumTick = ?";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute([$nextNumTick]);
                $result = $stmt->fetch();
                $ticketId = $result['IDtickets'] ?? null;
                error_log("DEBUG: ID récupéré par NumTick: $ticketId");
            } else {
                error_log("DEBUG: Ticket inséré avec ID (lastInsertId): $ticketId");
            }

            if (!$ticketId) {
                throw new Exception("Impossible de récupérer l'ID du ticket inséré");
            }

            // 5. Insérer les détails dans VteJour pour chaque article du panier
            $sql = "INSERT INTO VteJour (
                articles,
                quantite,
                total,
                IDarticle,
                NumCategories,
                Cuisine,
                IDtickets
            ) VALUES (?, ?, ?, ?, ?, ?, ?)";

            $stmt = $this->pdo->prepare($sql);

            foreach ($cart as $articleId => $item) {
                $article = $item['article'];
                $quantity = $item['quantity'];
                $itemTotal = $item['price'] * $quantity;

                error_log("DEBUG: Insertion VteJour - Article: {$article['designation']}, Qté: $quantity, Total: $itemTotal, ID: $articleId");

                $success = $stmt->execute([
                    $article['designation'],    // articles
                    $quantity,                  // quantite
                    $itemTotal,                // total
                    $articleId,                // IDarticle
                    $article['IDCategorie'],   // NumCategories
                    $article['Cuisine'] ?? '', // Cuisine
                    $ticketId                  // IDtickets
                ]);

                if (!$success) {
                    $errorInfo = $stmt->errorInfo();
                    error_log("DEBUG: Erreur SQL VteJour - " . print_r($errorInfo, true));
                    throw new Exception("Erreur lors de l'insertion des détails pour l'article ID: $articleId - " . $errorInfo[2]);
                }
            }

            // Valider la transaction
            $this->pdo->commit();
            error_log("DEBUG: Transaction validée avec succès");

            // Vérifier que le ticket existe bien en base
            $sql = "SELECT COUNT(*) as count FROM tickets WHERE IDtickets = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$ticketId]);
            $result = $stmt->fetch();

            if ($result['count'] > 0) {
                error_log("DEBUG: Ticket confirmé en base, ID: $ticketId");
            } else {
                error_log("ERREUR: Ticket non trouvé en base après commit, ID: $ticketId");
                return false;
            }

            // Retourner l'ID du ticket créé
            return $ticketId;

        } catch (Exception $e) {
            // Annuler la transaction en cas d'erreur
            $this->pdo->rollBack();
            error_log("Erreur saveOrder: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Convertir le mode de paiement en code
     */
    private function getPaymentTypeCode($paymentMethod) {
        switch (strtolower($paymentMethod)) {
            case 'cash':
            case 'especes':
                return 'ESP';
            case 'card':
            case 'carte':
                return 'CB';
            case 'check':
            case 'cheque':
                return 'CHQ';
            default:
                return 'ESP'; // Par défaut espèces
        }
    }

    /**
     * Récupérer l'ID du serveur actuel
     */
    public function getCurrentServerId() {
        // Récupérer depuis la session si défini
        if (isset($_SESSION['server_id'])) {
            return intval($_SESSION['server_id']);
        }

        // Valeur par défaut
        return 1;
    }

    /**
     * Récupérer l'ID de la table actuelle
     */
    public function getCurrentTableId() {
        // Récupérer depuis la session si défini
        if (isset($_SESSION['table_id'])) {
            return intval($_SESSION['table_id']);
        }

        // Valeur par défaut
        return 103;
    }

    /**
     * Définir le serveur actuel
     */
    public function setCurrentServer($serverId) {
        $_SESSION['server_id'] = intval($serverId);
    }

    /**
     * Définir la table actuelle
     */
    public function setCurrentTable($tableId) {
        $_SESSION['table_id'] = intval($tableId);
    }

    /**
     * Fonction de test pour valider une commande simple
     */
    public function testProcessOrder() {
        error_log("=== DÉBUT TEST SIMPLE PROCESSORDER ===");

        // Vider le panier et ajouter un article de test
        $this->clearCart();

        // Récupérer le premier article disponible
        try {
            $sql = "SELECT TOP 1 IDarticles, designation, prix, IDCategorie, Cuisine FROM articles";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $article = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$article) {
                error_log("ERREUR: Aucun article trouvé");
                return false;
            }

            error_log("Article de test: " . print_r($article, true));

            // Ajouter l'article au panier
            $this->addToCart($article['IDarticles'], 1);

            // Valider la commande
            $result = $this->processOrder('cash', 1, 103);

            error_log("Résultat processOrder: " . ($result ? "Succès (ID: $result)" : "Échec"));
            error_log("=== FIN TEST SIMPLE PROCESSORDER ===");

            return $result;

        } catch (Exception $e) {
            error_log("ERREUR testProcessOrder: " . $e->getMessage());
            error_log("=== FIN TEST SIMPLE PROCESSORDER (ERREUR) ===");
            return false;
        }
    }
    
    /**
     * Statistiques du jour depuis les fichiers JSON (ancienne méthode)
     */
    public function getTodayStats() {
        $ordersFile = 'orders/' . date('Y-m-d') . '.json';

        if (!file_exists($ordersFile)) {
            return [
                'orders_count' => 0,
                'total_revenue' => 0,
                'items_sold' => 0
            ];
        }

        $orders = json_decode(file_get_contents($ordersFile), true) ?? [];

        $stats = [
            'orders_count' => count($orders),
            'total_revenue' => 0,
            'items_sold' => 0
        ];

        foreach ($orders as $order) {
            $stats['total_revenue'] += $order['total_ttc'];
            foreach ($order['items'] as $item) {
                $stats['items_sold'] += $item['quantity'];
            }
        }

        return $stats;
    }

    /**
     * Statistiques du jour depuis la base de données (tables tickets et VteJour)
     */
    public function getTodayStatsFromDB() {
        if (!$this->connected) {
            return [
                'orders_count' => 0,
                'total_revenue' => 0,
                'items_sold' => 0
            ];
        }

        try {
            // Compter les tickets avec condition Djr = Vrai uniquement
            $sql = "SELECT COUNT(*) as orders_count, SUM(total) as total_revenue
                    FROM tickets
                    WHERE Djr = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([true]);
            $result = $stmt->fetch();

            $orders_count = $result['orders_count'] ?? 0;
            $total_revenue = $result['total_revenue'] ?? 0;

            // Compter les articles vendus avec condition Djr = Vrai uniquement
            $sql = "SELECT SUM(v.quantite) as items_sold
                    FROM VteJour v
                    INNER JOIN tickets t ON v.IDtickets = t.IDtickets
                    WHERE t.Djr = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([true]);
            $result = $stmt->fetch();

            $items_sold = $result['items_sold'] ?? 0;

            return [
                'orders_count' => intval($orders_count),
                'total_revenue' => floatval($total_revenue),
                'items_sold' => intval($items_sold)
            ];

        } catch (Exception $e) {
            error_log("Erreur getTodayStatsFromDB: " . $e->getMessage());
            return [
                'orders_count' => 0,
                'total_revenue' => 0,
                'items_sold' => 0
            ];
        }
    }

    /**
     * Statistiques de la semaine depuis la base de données
     */
    public function getWeekStatsFromDB() {
        if (!$this->connected) {
            return [];
        }

        try {
            $weekStats = [];

            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-$i days"));
                $dateNum = date('Ymd', strtotime("-$i days")); // Format numérique HFSQL

                // Statistiques du jour
                $sql = "SELECT COUNT(*) as orders_count, SUM(total) as total_revenue
                        FROM tickets
                        WHERE DATE = ?";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute([$dateNum]);
                $result = $stmt->fetch();

                $weekStats[] = [
                    'date' => $date,
                    'orders' => intval($result['orders_count'] ?? 0),
                    'revenue' => floatval($result['total_revenue'] ?? 0)
                ];
            }

            return $weekStats;

        } catch (Exception $e) {
            error_log("Erreur getWeekStatsFromDB: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Articles les plus vendus depuis la base de données
     */
    public function getTopSellingArticlesFromDB($limit = 10, $days = 7) {
        if (!$this->connected) {
            return [];
        }

        try {
            $dateLimitNum = date('Ymd', strtotime("-$days days")); // Format numérique HFSQL

            $sql = "SELECT
                        a.IDarticles,
                        a.designation,
                        c.categories as nom_categorie,
                        SUM(v.quantite) as total_vendu,
                        SUM(v.total) as chiffre_affaires
                    FROM VteJour v
                    INNER JOIN tickets t ON v.IDtickets = t.IDtickets
                    INNER JOIN articles a ON v.IDarticles = a.IDarticles
                    LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                    WHERE t.DATE >= ?
                    GROUP BY a.IDarticles, a.designation, c.categories
                    ORDER BY total_vendu DESC
                    LIMIT ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$dateLimitNum, $limit]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Erreur getTopSellingArticlesFromDB: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Statistiques détaillées par période
     */
    public function getDetailedStatsFromDB($startDate, $endDate) {
        if (!$this->connected) {
            return [
                'total_tickets' => 0,
                'total_revenue' => 0,
                'total_items' => 0,
                'average_ticket' => 0,
                'tickets_by_day' => []
            ];
        }

        try {
            // Convertir les dates au format numérique HFSQL
            $startDateNum = str_replace('-', '', $startDate); // 2025-07-15 -> 20250715
            $endDateNum = str_replace('-', '', $endDate);

            // Statistiques globales
            $sql = "SELECT
                        COUNT(*) as total_tickets,
                        SUM(total) as total_revenue,
                        AVG(total) as average_ticket
                    FROM tickets
                    WHERE DATE >= ? AND DATE <= ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$startDateNum, $endDateNum]);
            $globalStats = $stmt->fetch();

            // Total des articles vendus
            $sql = "SELECT SUM(v.quantite) as total_items
                    FROM VteJour v
                    INNER JOIN tickets t ON v.IDtickets = t.IDtickets
                    WHERE t.DATE >= ? AND t.DATE <= ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$startDateNum, $endDateNum]);
            $itemsStats = $stmt->fetch();

            // Tickets par jour
            $sql = "SELECT
                        DATE as date_num,
                        COUNT(*) as tickets_count,
                        SUM(total) as daily_revenue
                    FROM tickets
                    WHERE DATE >= ? AND DATE <= ?
                    GROUP BY DATE
                    ORDER BY DATE";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$startDateNum, $endDateNum]);
            $ticketsByDayRaw = $stmt->fetchAll();

            // Convertir les dates numériques en format lisible
            $ticketsByDay = [];
            foreach ($ticketsByDayRaw as $day) {
                $dateStr = $day['date_num'];
                $formattedDate = substr($dateStr, 0, 4) . '-' . substr($dateStr, 4, 2) . '-' . substr($dateStr, 6, 2);
                $ticketsByDay[] = [
                    'date' => $formattedDate,
                    'tickets_count' => $day['tickets_count'],
                    'daily_revenue' => $day['daily_revenue']
                ];
            }

            return [
                'total_tickets' => intval($globalStats['total_tickets'] ?? 0),
                'total_revenue' => floatval($globalStats['total_revenue'] ?? 0),
                'total_items' => intval($itemsStats['total_items'] ?? 0),
                'average_ticket' => floatval($globalStats['average_ticket'] ?? 0),
                'tickets_by_day' => $ticketsByDay
            ];

        } catch (Exception $e) {
            error_log("Erreur getDetailedStatsFromDB: " . $e->getMessage());
            return [
                'total_tickets' => 0,
                'total_revenue' => 0,
                'total_items' => 0,
                'average_ticket' => 0,
                'tickets_by_day' => []
            ];
        }
    }
}

// Initialisation globale du POS
$pos = new POSManager();

// Fonctions utilitaires
function formatPrice($price) {
    return number_format($price, 2, ',', ' ');
}

function formatDateTime($datetime) {
    return date('d/m/Y H:i', strtotime($datetime));
}
?>
