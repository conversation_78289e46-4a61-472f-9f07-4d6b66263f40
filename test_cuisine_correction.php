<?php
/**
 * Test de la correction de la colonne Cuisine
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🍽️ Test Correction Colonne Cuisine</h1>";

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'pos_config.php';

echo "<h2>1. Test des méthodes de récupération d'articles</h2>";

try {
    echo "<h3>1.1. Test getArticlesByCategory</h3>";
    $articles = $pos->getArticlesByCategory();
    
    if (!empty($articles)) {
        echo "✅ Articles récupérés: " . count($articles) . "<br>";
        
        // Afficher les 3 premiers articles avec leur valeur Cuisine
        $sampleArticles = array_slice($articles, 0, 3);
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Nom</th><th>Prix</th><th>Catégorie</th><th>Cuisine</th></tr>";
        
        foreach ($sampleArticles as $article) {
            echo "<tr>";
            echo "<td>{$article['IDarticles']}</td>";
            echo "<td>{$article['designation']}</td>";
            echo "<td>{$article['prix']}</td>";
            echo "<td>{$article['IDCategorie']}</td>";
            echo "<td><strong>" . ($article['Cuisine'] ?? 'NULL') . "</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Vérifier si au moins un article a une valeur Cuisine
        $articlesWithCuisine = array_filter($articles, function($article) {
            return !empty($article['Cuisine']);
        });
        
        if (!empty($articlesWithCuisine)) {
            echo "✅ " . count($articlesWithCuisine) . " article(s) ont une valeur Cuisine non vide<br>";
        } else {
            echo "⚠️ Aucun article n'a de valeur Cuisine non vide<br>";
        }
        
    } else {
        echo "❌ Aucun article récupéré<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur getArticlesByCategory: " . $e->getMessage() . "<br>";
}

echo "<h3>1.2. Test getArticleById</h3>";
if (!empty($articles)) {
    try {
        $testArticle = $articles[0];
        $articleById = $pos->getArticleById($testArticle['IDarticles']);
        
        if ($articleById) {
            echo "✅ Article récupéré par ID:<br>";
            echo "- ID: {$articleById['IDarticles']}<br>";
            echo "- Nom: {$articleById['designation']}<br>";
            echo "- Cuisine: <strong>" . ($articleById['Cuisine'] ?? 'NULL') . "</strong><br>";
            
            // Comparer avec l'article de la liste
            if (($testArticle['Cuisine'] ?? '') === ($articleById['Cuisine'] ?? '')) {
                echo "✅ Valeur Cuisine cohérente entre les deux méthodes<br>";
            } else {
                echo "⚠️ Valeur Cuisine différente:<br>";
                echo "  - getArticlesByCategory: '" . ($testArticle['Cuisine'] ?? 'NULL') . "'<br>";
                echo "  - getArticleById: '" . ($articleById['Cuisine'] ?? 'NULL') . "'<br>";
            }
        } else {
            echo "❌ Article non trouvé par getArticleById<br>";
        }
    } catch (Exception $e) {
        echo "❌ Erreur getArticleById: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>2. Test du panier avec valeurs Cuisine</h2>";

try {
    // Vider le panier et ajouter un article
    $pos->clearCart();
    echo "✅ Panier vidé<br>";
    
    if (!empty($articles)) {
        $testArticle = $articles[0];
        $result = $pos->addToCart($testArticle['IDarticles'], 1);
        
        if ($result === true || (is_array($result) && $result['success'])) {
            echo "✅ Article ajouté au panier<br>";
            
            $cart = $pos->getCart();
            foreach ($cart as $id => $item) {
                echo "<h4>Article dans le panier:</h4>";
                echo "- ID: $id<br>";
                echo "- Nom: {$item['article']['designation']}<br>";
                echo "- Cuisine: <strong>" . ($item['article']['Cuisine'] ?? 'NULL') . "</strong><br>";
                
                // Vérifier toutes les clés de l'article
                echo "<h5>Toutes les données de l'article:</h5>";
                foreach ($item['article'] as $key => $value) {
                    echo "- $key: " . ($value ?? 'NULL') . "<br>";
                }
            }
        } else {
            echo "❌ Impossible d'ajouter l'article au panier<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erreur test panier: " . $e->getMessage() . "<br>";
}

echo "<h2>3. Test de validation avec Cuisine</h2>";

try {
    if (!empty($cart)) {
        echo "🚀 Test de validation de commande...<br>";
        
        $orderId = $pos->processOrder('cash');
        
        if ($orderId) {
            echo "✅ Commande validée avec ID: $orderId<br>";
            
            // Vérifier les données dans VteJour
            echo "<h4>Vérification VteJour:</h4>";
            $sql = "SELECT articles, quantite, total, IDarticle, NumCategories, Cuisine, IDtickets FROM VteJour WHERE IDtickets = ?";
            $stmt = $pos->pdo->prepare($sql);
            $stmt->execute([$orderId]);
            $vteJourData = $stmt->fetchAll();
            
            if (!empty($vteJourData)) {
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr><th>Article</th><th>Qté</th><th>Total</th><th>IDarticle</th><th>Catégorie</th><th>Cuisine</th></tr>";
                
                foreach ($vteJourData as $row) {
                    echo "<tr>";
                    echo "<td>{$row['articles']}</td>";
                    echo "<td>{$row['quantite']}</td>";
                    echo "<td>{$row['total']}</td>";
                    echo "<td>{$row['IDarticle']}</td>";
                    echo "<td>{$row['NumCategories']}</td>";
                    echo "<td><strong style='color: " . (empty($row['Cuisine']) ? 'red' : 'green') . ";'>" . ($row['Cuisine'] ?: 'VIDE') . "</strong></td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Vérifier si toutes les valeurs Cuisine sont remplies
                $emptyCount = 0;
                $filledCount = 0;
                
                foreach ($vteJourData as $row) {
                    if (empty($row['Cuisine'])) {
                        $emptyCount++;
                    } else {
                        $filledCount++;
                    }
                }
                
                if ($emptyCount === 0) {
                    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
                    echo "🎉 <strong>PARFAIT !</strong><br>";
                    echo "Toutes les valeurs Cuisine sont correctement remplies !";
                    echo "</div>";
                } else {
                    echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px;'>";
                    echo "⚠️ <strong>Résultat mitigé</strong><br>";
                    echo "- Valeurs Cuisine remplies: $filledCount<br>";
                    echo "- Valeurs Cuisine vides: $emptyCount<br>";
                    echo "</div>";
                }
                
            } else {
                echo "❌ Aucune donnée trouvée dans VteJour<br>";
            }
            
        } else {
            echo "❌ Échec de la validation<br>";
        }
    } else {
        echo "⚠️ Panier vide, impossible de tester la validation<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur test validation: " . $e->getMessage() . "<br>";
}

echo "<h2>4. Recommandations</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
echo "<h3>📋 Si la colonne Cuisine est encore vide:</h3>";
echo "<ol>";
echo "<li><strong>Vérifiez la base de données:</strong> Les articles ont-ils vraiment des valeurs dans la colonne Cuisine ?</li>";
echo "<li><strong>Valeur par défaut:</strong> Si les articles n'ont pas de Cuisine, vous pouvez définir une valeur par défaut</li>";
echo "<li><strong>Utiliser la catégorie:</strong> Vous pouvez utiliser le nom de la catégorie comme valeur Cuisine</li>";
echo "</ol>";

echo "<h3>🔧 Solutions possibles:</h3>";
echo "<ul>";
echo "<li><strong>Valeur par défaut:</strong> Changer <code>\$article['Cuisine'] ?? ''</code> en <code>\$article['Cuisine'] ?? 'GENERAL'</code></li>";
echo "<li><strong>Utiliser catégorie:</strong> Changer en <code>\$article['nom_categorie'] ?? 'GENERAL'</code></li>";
echo "<li><strong>Laisser vide:</strong> Si c'est acceptable pour votre système</li>";
echo "</ul>";
echo "</div>";

echo "<h2>✅ Test terminé</h2>";
echo "<p>Vérifiez les résultats ci-dessus pour voir si la colonne Cuisine est maintenant correctement remplie.</p>";
?>
