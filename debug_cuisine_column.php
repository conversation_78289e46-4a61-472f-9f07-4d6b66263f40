<?php
/**
 * Debug de la colonne Cuisine dans VteJour
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Debug Colonne Cuisine</h1>";

try {
    $pdo = new PDO('odbc:DataCafe', 'admin', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "✅ Connexion réussie<br>";
} catch (Exception $e) {
    echo "❌ Erreur connexion: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>1. Vérification structure table articles</h2>";

try {
    $sql = "SELECT TOP 5 IDarticles, designation, prix, IDCategorie, Cuisine FROM articles WHERE Cuisine IS NOT NULL AND Cuisine != ''";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $articles = $stmt->fetchAll();
    
    if (!empty($articles)) {
        echo "✅ Articles avec valeur Cuisine trouvés:<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Désignation</th><th>Prix</th><th>Catégorie</th><th>Cuisine</th></tr>";
        foreach ($articles as $article) {
            echo "<tr>";
            echo "<td>{$article['IDarticles']}</td>";
            echo "<td>{$article['designation']}</td>";
            echo "<td>{$article['prix']}</td>";
            echo "<td>{$article['IDCategorie']}</td>";
            echo "<td><strong>" . ($article['Cuisine'] ?? 'NULL') . "</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "⚠️ Aucun article avec valeur Cuisine trouvé<br>";
        
        // Vérifier tous les articles
        $sql = "SELECT TOP 5 IDarticles, designation, prix, IDCategorie, Cuisine FROM articles";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $allArticles = $stmt->fetchAll();
        
        echo "<h3>Échantillon de tous les articles:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Désignation</th><th>Prix</th><th>Catégorie</th><th>Cuisine</th></tr>";
        foreach ($allArticles as $article) {
            echo "<tr>";
            echo "<td>{$article['IDarticles']}</td>";
            echo "<td>{$article['designation']}</td>";
            echo "<td>{$article['prix']}</td>";
            echo "<td>{$article['IDCategorie']}</td>";
            echo "<td>" . ($article['Cuisine'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ Erreur vérification articles: " . $e->getMessage() . "<br>";
}

echo "<h2>2. Vérification des données dans le panier</h2>";

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'pos_config.php';

try {
    // Préparer un panier de test
    $pos->clearCart();
    $articles = $pos->getArticlesByCategory();
    
    if (!empty($articles)) {
        $testArticle = $articles[0];
        echo "Article de test sélectionné:<br>";
        echo "- ID: {$testArticle['IDarticles']}<br>";
        echo "- Nom: {$testArticle['designation']}<br>";
        echo "- Cuisine: " . ($testArticle['Cuisine'] ?? 'NULL') . "<br>";
        
        $pos->addToCart($testArticle['IDarticles'], 1);
        $cart = $pos->getCart();
        
        echo "<h3>Données dans le panier:</h3>";
        foreach ($cart as $id => $item) {
            echo "Article ID: $id<br>";
            echo "- Nom: {$item['article']['designation']}<br>";
            echo "- Cuisine dans panier: " . ($item['article']['Cuisine'] ?? 'NULL') . "<br>";
            
            // Vérifier si la clé existe
            if (array_key_exists('Cuisine', $item['article'])) {
                echo "- Clé 'Cuisine' existe: OUI<br>";
                echo "- Valeur: '" . $item['article']['Cuisine'] . "'<br>";
                echo "- Type: " . gettype($item['article']['Cuisine']) . "<br>";
                echo "- Vide?: " . (empty($item['article']['Cuisine']) ? 'OUI' : 'NON') . "<br>";
            } else {
                echo "- Clé 'Cuisine' existe: NON<br>";
            }
        }
    }
} catch (Exception $e) {
    echo "❌ Erreur test panier: " . $e->getMessage() . "<br>";
}

echo "<h2>3. Test de la méthode getArticleById</h2>";

if (isset($testArticle)) {
    try {
        $articleFromDB = $pos->getArticleById($testArticle['IDarticles']);
        
        if ($articleFromDB) {
            echo "Article récupéré par getArticleById:<br>";
            echo "- ID: {$articleFromDB['IDarticles']}<br>";
            echo "- Nom: {$articleFromDB['designation']}<br>";
            echo "- Cuisine: " . ($articleFromDB['Cuisine'] ?? 'NULL') . "<br>";
            
            echo "<h4>Toutes les clés de l'article:</h4>";
            foreach ($articleFromDB as $key => $value) {
                echo "- $key: " . ($value ?? 'NULL') . "<br>";
            }
        } else {
            echo "❌ Article non trouvé par getArticleById<br>";
        }
    } catch (Exception $e) {
        echo "❌ Erreur getArticleById: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>4. Vérification des données VteJour existantes</h2>";

try {
    $sql = "SELECT TOP 5 articles, quantite, total, IDarticle, NumCategories, Cuisine, IDtickets FROM VteJour ORDER BY IDVteJour DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $vteJourData = $stmt->fetchAll();
    
    if (!empty($vteJourData)) {
        echo "Dernières entrées VteJour:<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Article</th><th>Qté</th><th>Total</th><th>IDarticle</th><th>Catégorie</th><th>Cuisine</th><th>Ticket</th></tr>";
        foreach ($vteJourData as $row) {
            echo "<tr>";
            echo "<td>{$row['articles']}</td>";
            echo "<td>{$row['quantite']}</td>";
            echo "<td>{$row['total']}</td>";
            echo "<td>{$row['IDarticle']}</td>";
            echo "<td>{$row['NumCategories']}</td>";
            echo "<td><strong>" . ($row['Cuisine'] ?? 'NULL') . "</strong></td>";
            echo "<td>{$row['IDtickets']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "⚠️ Aucune donnée VteJour trouvée<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur vérification VteJour: " . $e->getMessage() . "<br>";
}

echo "<h2>5. Solutions possibles</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<h3>🔧 Solutions pour corriger la colonne Cuisine:</h3>";
echo "<ol>";
echo "<li><strong>Si les articles n'ont pas de valeur Cuisine:</strong>";
echo "<ul>";
echo "<li>Mettre une valeur par défaut (ex: 'GENERAL', 'STANDARD')</li>";
echo "<li>Utiliser la catégorie comme valeur Cuisine</li>";
echo "<li>Laisser vide si c'est acceptable</li>";
echo "</ul></li>";

echo "<li><strong>Si les articles ont une valeur Cuisine mais elle n'est pas récupérée:</strong>";
echo "<ul>";
echo "<li>Vérifier la requête SQL dans getArticleById</li>";
echo "<li>Vérifier la requête SQL dans getArticlesByCategory</li>";
echo "</ul></li>";

echo "<li><strong>Si la valeur est récupérée mais mal transmise:</strong>";
echo "<ul>";
echo "<li>Vérifier le code d'insertion dans VteJour</li>";
echo "<li>Ajouter des logs pour tracer la valeur</li>";
echo "</ul></li>";
echo "</ol>";
echo "</div>";

echo "<h2>✅ Debug terminé</h2>";
echo "<p>Vérifiez les résultats ci-dessus pour identifier pourquoi la colonne Cuisine est vide.</p>";
?>
