<?php
/**
 * Test simple de validation de commande
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'pos_config.php';

echo "<h1>Test Simple de Validation</h1>";

$pos = new POSConfig();

if (!$pos->isConnected()) {
    echo "❌ Pas de connexion à la base<br>";
    exit;
}

echo "✅ Connexion OK<br>";

// Test de la fonction simple
echo "<h2>Test de validation simple</h2>";
$result = $pos->testProcessOrder();

if ($result) {
    echo "✅ Commande validée avec succès ! ID: $result<br>";
} else {
    echo "❌ Échec de la validation<br>";
}

// Vérifier si un ticket a été créé
echo "<h2>Vérification en base</h2>";
try {
    $sql = "SELECT TOP 5 IDtickets, NumTick, DATE, heure, total FROM tickets ORDER BY IDtickets DESC";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Derniers tickets créés:<br>";
    foreach ($tickets as $ticket) {
        echo "- ID: {$ticket['IDtickets']}, Num: {$ticket['NumTick']}, Date: {$ticket['DATE']}, Heure: {$ticket['heure']}, Total: {$ticket['total']}<br>";
    }
} catch (Exception $e) {
    echo "Erreur lors de la vérification: " . $e->getMessage() . "<br>";
}

// Afficher les logs récents
echo "<h2>Logs récents</h2>";
$logFile = ini_get('error_log');
if ($logFile && file_exists($logFile)) {
    $lines = file($logFile);
    $recentLines = array_slice($lines, -20);
    echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 200px; overflow-y: scroll;'>";
    foreach ($recentLines as $line) {
        if (strpos($line, 'DEBUG:') !== false || strpos($line, 'ERREUR') !== false || strpos($line, 'TEST') !== false) {
            echo htmlspecialchars($line);
        }
    }
    echo "</pre>";
} else {
    echo "Logs non accessibles<br>";
}

?>
