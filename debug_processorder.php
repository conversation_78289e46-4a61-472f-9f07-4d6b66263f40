<?php
/**
 * Debug spécifique pour processOrder
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Démarrer la session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔍 Debug ProcessOrder</h1>";

// Inclure le fichier de config
require_once 'pos_config.php';

// Créer l'instance POS
$pos = new POSConfig();

echo "<h2>1. Vérifications de base</h2>";
echo "✅ Connexion: " . ($pos->isConnected() ? "OK" : "ÉCHEC") . "<br>";

// Vider et remplir le panier
echo "<h2>2. Préparation du panier</h2>";
$pos->clearCart();
echo "✅ Panier vidé<br>";

// Ajouter un article
try {
    $sql = "SELECT TOP 1 IDarticle, designation, prix, IDCategorie, Cuisine FROM articles WHERE prix > 0";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $article = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($article) {
        $pos->addToCart($article['IDarticle'], 1);
        echo "✅ Article ajouté: " . $article['designation'] . "<br>";
    } else {
        echo "❌ Aucun article trouvé<br>";
        exit;
    }
} catch (Exception $e) {
    echo "❌ Erreur article: " . $e->getMessage() . "<br>";
    exit;
}

// Vérifier le panier
$cart = $pos->getCart();
echo "✅ Panier contient " . count($cart) . " article(s)<br>";

echo "<h2>3. Test processOrder étape par étape</h2>";

// Activer le logging détaillé
error_log("=== DÉBUT DEBUG PROCESSORDER ===");

try {
    // Étape 1: Vérifier le panier
    echo "Étape 1: Vérification du panier...<br>";
    if (empty($cart)) {
        echo "❌ Panier vide<br>";
        exit;
    }
    echo "✅ Panier OK<br>";
    
    // Étape 2: Calculer le total
    echo "Étape 2: Calcul du total...<br>";
    $total = $pos->getCartTotalTTC();
    echo "✅ Total: $total<br>";
    
    // Étape 3: Test de connexion PDO
    echo "Étape 3: Test PDO...<br>";
    $sql = "SELECT 1 as test";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    echo "✅ PDO OK<br>";
    
    // Étape 4: Test transaction
    echo "Étape 4: Test transaction...<br>";
    $pos->pdo->beginTransaction();
    echo "✅ Transaction démarrée<br>";
    $pos->pdo->rollBack();
    echo "✅ Transaction annulée (test)<br>";
    
    // Étape 5: Test NumTick
    echo "Étape 5: Test NumTick...<br>";
    $sql = "SELECT MAX(NumTick) as last_num FROM tickets";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    $nextNumTick = ($result['last_num'] ?? 0) + 1;
    echo "✅ Prochain NumTick: $nextNumTick<br>";
    
    // Étape 6: Test insertion ticket (sans commit)
    echo "Étape 6: Test insertion ticket...<br>";
    $pos->pdo->beginTransaction();
    
    $sql = "INSERT INTO tickets (NumTick, IDServeur, IDTables, DATE, heure, total, typepaye, Djr, ImpTick) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $pos->pdo->prepare($sql);
    
    $insertData = [
        intval($nextNumTick),
        1,
        103,
        intval(date('Ymd')),
        intval(date('His')),
        floatval($total),
        'ESP',
        1,
        1
    ];
    
    echo "Données à insérer: <pre>" . print_r($insertData, true) . "</pre>";
    
    $success = $stmt->execute($insertData);
    
    if ($success) {
        $ticketId = $pos->pdo->lastInsertId();
        echo "✅ Insertion ticket OK, ID: $ticketId<br>";
        
        // Test insertion VteJour
        echo "Étape 7: Test insertion VteJour...<br>";
        $sql = "INSERT INTO VteJour (articles, quantite, total, IDarticle, NumCategories, Cuisine, IDtickets) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $pos->pdo->prepare($sql);
        
        foreach ($cart as $articleId => $item) {
            $article = $item['article'];
            $quantity = $item['quantity'];
            $itemTotal = $item['price'] * $quantity;
            
            $vteData = [
                $article['designation'],
                $quantity,
                $itemTotal,
                $articleId,
                $article['IDCategorie'],
                $article['Cuisine'] ?? '',
                $ticketId
            ];
            
            echo "Données VteJour: <pre>" . print_r($vteData, true) . "</pre>";
            
            $success = $stmt->execute($vteData);
            
            if ($success) {
                echo "✅ Insertion VteJour OK pour article $articleId<br>";
            } else {
                $errorInfo = $stmt->errorInfo();
                echo "❌ Erreur VteJour: " . print_r($errorInfo, true) . "<br>";
                $pos->pdo->rollBack();
                exit;
            }
        }
        
        // Commit de test
        echo "Étape 8: Commit...<br>";
        $pos->pdo->commit();
        echo "✅ Transaction validée<br>";
        
        // Vider le panier
        echo "Étape 9: Vidage du panier...<br>";
        $pos->clearCart();
        $cartAfter = $pos->getCart();
        echo "✅ Panier vidé (" . count($cartAfter) . " articles restants)<br>";
        
        echo "<h2>🎉 SUCCÈS COMPLET!</h2>";
        echo "Ticket créé avec ID: $ticketId<br>";
        
    } else {
        $errorInfo = $stmt->errorInfo();
        echo "❌ Erreur insertion ticket: <pre>" . print_r($errorInfo, true) . "</pre>";
        $pos->pdo->rollBack();
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
    if ($pos->pdo->inTransaction()) {
        $pos->pdo->rollBack();
    }
}

error_log("=== FIN DEBUG PROCESSORDER ===");

echo "<h2>4. Test processOrder complet</h2>";

// Remettre un article dans le panier
$pos->addToCart($article['IDarticle'], 1);

echo "Test de la méthode processOrder()...<br>";
$orderId = $pos->processOrder('cash');

if ($orderId) {
    echo "✅ processOrder réussi! ID: $orderId<br>";
} else {
    echo "❌ processOrder échoué<br>";
}

// Afficher les logs récents
echo "<h2>5. Logs récents</h2>";
$logFile = ini_get('error_log');
if ($logFile && file_exists($logFile)) {
    $lines = file($logFile);
    $recentLines = array_slice($lines, -50);
    echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: scroll;'>";
    foreach ($recentLines as $line) {
        if (strpos($line, 'DEBUG:') !== false || strpos($line, 'ERREUR') !== false) {
            echo htmlspecialchars($line);
        }
    }
    echo "</pre>";
} else {
    echo "Logs non accessibles<br>";
}

?>
