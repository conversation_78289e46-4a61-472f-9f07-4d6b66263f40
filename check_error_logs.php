<?php
/**
 * Vérifier les logs d'erreur PHP pour identifier les problèmes
 */

echo "<h1>📋 Vérification des logs d'erreur</h1>";

echo "<h2>1. Configuration des logs PHP</h2>";
echo "log_errors: " . (ini_get('log_errors') ? 'Activé' : 'Désactivé') . "<br>";
echo "error_log: " . (ini_get('error_log') ?: 'Par défaut') . "<br>";

echo "<h2>2. Logs d'erreur récents</h2>";

// Chercher les fichiers de logs possibles
$possibleLogFiles = [
    ini_get('error_log'),
    'error_log',
    'php_errors.log',
    '../logs/error.log',
    '../logs/php_errors.log',
    'C:/xampp/php/logs/php_error_log',
    'C:/xampp/apache/logs/error.log'
];

$foundLogs = [];

foreach ($possibleLogFiles as $logFile) {
    if ($logFile && file_exists($logFile) && is_readable($logFile)) {
        $foundLogs[] = $logFile;
    }
}

if (empty($foundLogs)) {
    echo "⚠️ Aucun fichier de log trouvé ou accessible<br>";
    echo "<h3>Logs possibles à vérifier manuellement:</h3>";
    echo "<ul>";
    foreach ($possibleLogFiles as $logFile) {
        if ($logFile) {
            echo "<li>$logFile</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<h3>Fichiers de logs trouvés:</h3>";
    foreach ($foundLogs as $logFile) {
        echo "<h4>$logFile</h4>";
        
        try {
            $content = file_get_contents($logFile);
            $lines = explode("\n", $content);
            
            // Prendre les 50 dernières lignes
            $recentLines = array_slice($lines, -50);
            
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
            echo "<pre>";
            foreach ($recentLines as $line) {
                if (trim($line)) {
                    // Mettre en évidence les erreurs liées à notre application
                    if (stripos($line, 'pos_config') !== false || 
                        stripos($line, 'saveOrder') !== false || 
                        stripos($line, 'processOrder') !== false ||
                        stripos($line, 'IDarticle') !== false) {
                        echo "<span style='background: yellow;'>" . htmlspecialchars($line) . "</span>\n";
                    } else {
                        echo htmlspecialchars($line) . "\n";
                    }
                }
            }
            echo "</pre>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "❌ Erreur lecture du fichier: " . $e->getMessage() . "<br>";
        }
    }
}

echo "<h2>3. Test avec logging forcé</h2>";

// Forcer l'écriture d'un log de test
error_log("=== TEST DEBUG POS - " . date('Y-m-d H:i:s') . " ===");

try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    require_once 'pos_config.php';
    
    // Préparer un panier simple
    $pos->clearCart();
    $articles = $pos->getArticlesByCategory();
    if (!empty($articles)) {
        $testArticle = $articles[0];
        $pos->addToCart($testArticle['IDarticles'], 1);
        
        error_log("DEBUG: Panier préparé avec article ID: " . $testArticle['IDarticles']);
        
        // Tenter processOrder avec logging
        error_log("DEBUG: Début processOrder");
        $result = $pos->processOrder('cash');
        error_log("DEBUG: Fin processOrder, résultat: " . ($result ? "Succès (ID: $result)" : "Échec"));
        
        if ($result) {
            echo "✅ processOrder a réussi avec ID: $result<br>";
        } else {
            echo "❌ processOrder a échoué<br>";
        }
    }
    
} catch (Exception $e) {
    error_log("DEBUG: Exception dans processOrder: " . $e->getMessage());
    echo "❌ Exception: " . $e->getMessage() . "<br>";
}

error_log("=== FIN TEST DEBUG POS ===");

echo "<h2>4. Instructions pour vérifier les logs</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<h3>📋 Comment vérifier les logs manuellement:</h3>";
echo "<ol>";
echo "<li><strong>Dans XAMPP:</strong> Ouvrez le panneau de contrôle XAMPP</li>";
echo "<li><strong>Apache:</strong> Cliquez sur 'Logs' à côté d'Apache</li>";
echo "<li><strong>PHP:</strong> Cherchez dans C:/xampp/php/logs/</li>";
echo "<li><strong>Recherchez:</strong> Les lignes contenant 'saveOrder', 'processOrder', ou 'IDarticle'</li>";
echo "</ol>";

echo "<h3>🔍 Que chercher:</h3>";
echo "<ul>";
echo "<li>Messages commençant par 'DEBUG:'</li>";
echo "<li>Messages d'erreur SQL</li>";
echo "<li>Messages 'Erreur saveOrder:'</li>";
echo "<li>Toute mention de 'IDarticle' ou 'VteJour'</li>";
echo "</ul>";
echo "</div>";

echo "<h2>✅ Vérification terminée</h2>";
echo "<p>Vérifiez les logs ci-dessus et exécutez aussi debug_saveorder_detaille.php</p>";
?>
