<?php
/**
 * Page Paramètres - Gestion du système POS
 */

require_once 'pos_config.php';

// Vérifier la connexion
if (!$pos->isConnected()) {
    die("Erreur de connexion à la base de données");
}

// Messages de feedback
$success_message = '';
$error_message = '';

if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

if (isset($_SESSION['error_message'])) {
    $error_message = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paramètres - BeCoffe POS</title>
    
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.2);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .page-title {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title h1 {
            font-size: 36px;
            font-weight: 700;
            color: white;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-title p {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .param-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .param-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .param-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            position: relative;
        }

        .param-card:nth-child(1) .card-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .param-card:nth-child(2) .card-icon {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .param-card:nth-child(3) .card-icon {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .param-card:nth-child(4) .card-icon {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
        }

        .card-title {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .card-description {
            font-size: 16px;
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .card-button {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .card-button:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .nav-links {
                flex-direction: column;
                gap: 10px;
            }

            .page-title h1 {
                font-size: 28px;
            }

            .cards-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .param-card {
                padding: 30px 20px;
            }

            .product-section {
                transform: translateY(calc(100% - 70px));
            }

            .product-section.expanded {
                transform: translateY(0);
            }
        }

        /* Popup de gestion des produits */
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
        }

        .popup-overlay.show {
            display: flex;
        }

        .popup-content {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: popupSlideIn 0.3s ease;
        }

        @keyframes popupSlideIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-50px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .popup-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .popup-title {
            font-size: 20px;
            font-weight: 700;
            margin: 0;
        }

        .popup-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .popup-close:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        .popup-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
            box-sizing: border-box;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .image-upload-area {
            border: 2px dashed #e9ecef;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .image-upload-area:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .image-upload-area.has-image {
            border-style: solid;
            border-color: #28a745;
            background: #f8fff9;
        }

        .upload-icon {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #6c757d;
            font-size: 14px;
        }

        .image-preview {
            max-width: 150px;
            max-height: 150px;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .popup-footer {
            background: #f8f9fa;
            padding: 20px 30px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .btn-cancel {
            padding: 12px 25px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-cancel:hover {
            background: #5a6268;
        }

        .btn-save-product {
            padding: 12px 25px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-save-product:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-cogs"></i>
            </div>
            <div class="nav-links">
                <a href="pos_mobile.php" class="nav-link">
                    <i class="fas fa-cash-register"></i> Retour POS
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <!-- Titre de la page -->
        <div class="page-title">
            <p>Gérez les différents aspects de votre point de vente</p>
        </div>

        <!-- Grille des cartes -->
        <div class="cards-grid">
            <!-- Carte 1: Gestion des serveurs -->
            <div class="param-card">
                <div class="card-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="card-title">Gestion des serveurs</h3>
                <p class="card-description">
                    Ajoutez, modifiez ou supprimez les serveurs. 
                    Gérez les permissions et les accès au système.
                </p>
                <button class="card-button" onclick="alert('Fonctionnalité en développement')">
                    <i class="fas fa-user-cog"></i>
                    Gérer les serveurs
                </button>
            </div>

            <!-- Carte 2: Gestion des produits -->
            <div class="param-card">
                <div class="card-icon">
                    <i class="fas fa-box"></i>
                </div>
                <h3 class="card-title">Gestion des produits</h3>
                <p class="card-description">
                    Ajoutez de nouveaux articles, modifiez les prix, 
                    gérez les stocks et les catégories.
                </p>
                <button class="card-button" onclick="openProductPopup()">
                    <i class="fas fa-edit"></i>
                    Gérer les produits
                </button>
            </div>

            <!-- Carte 3: Historique -->
            <div class="param-card">
                <div class="card-icon">
                    <i class="fas fa-history"></i>
                </div>
                <h3 class="card-title">Historique des ventes</h3>
                <p class="card-description">
                    Consultez l'historique complet des commandes, 
                    recherchez par période, serveur ou mode de paiement.
                </p>
                <a href="historique.php" class="card-button">
                    <i class="fas fa-search"></i>
                    Voir l'historique
                </a>
            </div>

            <!-- Carte 4: Statistiques -->
            <div class="param-card">
                <div class="card-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="card-title">Analyses & Statistiques</h3>
                <p class="card-description">
                    Analysez les performances de vente, 
                    consultez les graphiques et les rapports détaillés.
                </p>
                <a href="analyses.php" class="card-button">
                    <i class="fas fa-analytics"></i>
                    Voir les analyses
                </a>
            </div>
        </div>
    </div>

    <!-- Popup de gestion des produits -->
    <div class="popup-overlay" id="productPopup">
        <div class="popup-content">
            <!-- Header -->
            <div class="popup-header">
                <h2 class="popup-title">
                    <i class="fas fa-box"></i> Gestion des produits
                </h2>
                <button class="popup-close" onclick="closeProductPopup()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Body avec formulaire -->
            <div class="popup-body">
                <form id="productForm" method="POST" enctype="multipart/form-data">
                    <!-- Image du produit -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-image"></i> Image du produit
                        </label>
                        <div class="image-upload-area" onclick="document.getElementById('imageInput').click()">
                            <div id="imagePreview">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <div class="upload-text">Cliquez pour sélectionner une image</div>
                            </div>
                        </div>
                        <input type="file" id="imageInput" name="image" accept="image/*" style="display: none;" onchange="previewImage(this)">
                    </div>

                    <!-- Désignation -->
                    <div class="form-group">
                        <label class="form-label" for="designation">
                            <i class="fas fa-tag"></i> Désignation
                        </label>
                        <input type="text" id="designation" name="designation" class="form-input" placeholder="Nom du produit" required>
                    </div>

                    <!-- Catégorie -->
                    <div class="form-group">
                        <label class="form-label" for="categorie">
                            <i class="fas fa-list"></i> Catégorie
                        </label>
                        <select id="categorie" name="IDCategorie" class="form-select" required>
                            <option value="">Sélectionner une catégorie</option>
                            <?php
                            try {
                                $stmt = $pos->pdo->query("SELECT IDCategorie, categories FROM Categorie ORDER BY categories");
                                while ($cat = $stmt->fetch()) {
                                    echo "<option value='{$cat['IDCategorie']}'>{$cat['categories']}</option>";
                                }
                            } catch (Exception $e) {
                                echo "<option value=''>Erreur de chargement</option>";
                            }
                            ?>
                        </select>
                    </div>

                    <!-- Prix -->
                    <div class="form-group">
                        <label class="form-label" for="prix">
                            <i class="fas fa-coins"></i> Prix
                        </label>
                        <input type="number" id="prix" name="prix" class="form-input" placeholder="0.00" step="0.01" min="0" required>
                    </div>

                    <!-- Quantité -->
                    <div class="form-group">
                        <label class="form-label" for="quantite">
                            <i class="fas fa-boxes"></i> Quantité en stock
                        </label>
                        <input type="number" id="quantite" name="quantite" class="form-input" placeholder="0" min="0" required>
                    </div>

                    <!-- Unité -->
                    <div class="form-group">
                        <label class="form-label" for="unite">
                            <i class="fas fa-balance-scale"></i> Unité
                        </label>
                        <select id="unite" name="unite" class="form-select">
                            <option value="">Sélectionner une unité</option>
                            <option value="pièce">Pièce</option>
                            <option value="kg">Kilogramme</option>
                            <option value="g">Gramme</option>
                            <option value="l">Litre</option>
                            <option value="ml">Millilitre</option>
                            <option value="portion">Portion</option>
                        </select>
                    </div>

                    <!-- Cuisine -->
                    <div class="form-group">
                        <label class="form-label" for="cuisine">
                            <i class="fas fa-utensils"></i> Type de cuisine
                        </label>
                        <select id="cuisine" name="Cuisine" class="form-select">
                            <option value="">Sélectionner un type</option>
                            <option value="Chaude">Cuisine chaude</option>
                            <option value="Froide">Cuisine froide</option>
                            <option value="Boisson">Boisson</option>
                            <option value="Dessert">Dessert</option>
                            <option value="Autre">Autre</option>
                        </select>
                    </div>

                    <input type="hidden" id="productId" name="productId" value="">
                </form>
            </div>

            <!-- Footer avec boutons -->
            <div class="popup-footer">
                <button type="button" class="btn-cancel" onclick="closeProductPopup()">
                    <i class="fas fa-times"></i> Annuler
                </button>
                <button type="submit" form="productForm" class="btn-save-product">
                    <i class="fas fa-save"></i>
                    <span id="saveButtonText">Ajouter le produit</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Gestion de la popup
        function openProductPopup() {
            const popup = document.getElementById('productPopup');
            popup.classList.add('show');
            document.body.style.overflow = 'hidden'; // Empêcher le scroll de la page
        }

        function closeProductPopup() {
            const popup = document.getElementById('productPopup');
            popup.classList.remove('show');
            document.body.style.overflow = ''; // Restaurer le scroll
            resetForm();
        }

        // Fermer en cliquant sur l'overlay
        document.getElementById('productPopup').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProductPopup();
            }
        });

        // Fermer avec la touche Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeProductPopup();
            }
        });

        // Réinitialiser le formulaire
        function resetForm() {
            document.getElementById('productForm').reset();
            document.getElementById('imagePreview').innerHTML = `
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <div class="upload-text">Cliquez pour sélectionner une image</div>
            `;
            document.querySelector('.image-upload-area').classList.remove('has-image');
            document.getElementById('productId').value = '';
            document.getElementById('saveButtonText').textContent = 'Ajouter le produit';
        }

        // Prévisualisation de l'image
        function previewImage(input) {
            const preview = document.getElementById('imagePreview');
            const uploadArea = input.closest('.image-upload-area');

            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="Aperçu" class="image-preview">
                        <div class="upload-text">Cliquez pour changer l'image</div>
                    `;
                    uploadArea.classList.add('has-image');
                };

                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>
