<?php
/**
 * Test de la base de données et des tables
 */

require_once 'pos_config.php';

echo "<h1>Test de la base de données</h1>";

$pos = new POSConfig();

// Test de connexion
echo "<h2>1. Test de connexion</h2>";
if ($pos->isConnected()) {
    echo "✅ Connexion réussie<br>";
} else {
    echo "❌ Échec de connexion<br>";
    exit;
}

// Test structure table tickets
echo "<h2>2. Structure table tickets</h2>";
try {
    $sql = "SELECT TOP 1 * FROM tickets";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "✅ Table tickets accessible<br>";
        echo "Colonnes disponibles: " . implode(', ', array_keys($result)) . "<br>";
    } else {
        echo "⚠️ Table tickets vide<br>";
        
        // Essayer de voir la structure
        $sql = "SELECT * FROM tickets WHERE 1=0";
        $stmt = $pos->pdo->prepare($sql);
        $stmt->execute();
        
        for ($i = 0; $i < $stmt->columnCount(); $i++) {
            $meta = $stmt->getColumnMeta($i);
            echo "Colonne: " . $meta['name'] . " (Type: " . $meta['native_type'] . ")<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erreur table tickets: " . $e->getMessage() . "<br>";
}

// Test structure table VteJour
echo "<h2>3. Structure table VteJour</h2>";
try {
    $sql = "SELECT TOP 1 * FROM VteJour";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "✅ Table VteJour accessible<br>";
        echo "Colonnes disponibles: " . implode(', ', array_keys($result)) . "<br>";
    } else {
        echo "⚠️ Table VteJour vide<br>";
        
        // Essayer de voir la structure
        $sql = "SELECT * FROM VteJour WHERE 1=0";
        $stmt = $pos->pdo->prepare($sql);
        $stmt->execute();
        
        for ($i = 0; $i < $stmt->columnCount(); $i++) {
            $meta = $stmt->getColumnMeta($i);
            echo "Colonne: " . $meta['name'] . " (Type: " . $meta['native_type'] . ")<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erreur table VteJour: " . $e->getMessage() . "<br>";
}

// Test du panier
echo "<h2>4. Test du panier</h2>";
$cart = $pos->getCart();
if (empty($cart)) {
    echo "⚠️ Panier vide<br>";
    
    // Ajouter un article de test
    echo "Ajout d'un article de test...<br>";
    $pos->addToCart(1, 1); // Article ID 1, quantité 1
    $cart = $pos->getCart();
    
    if (!empty($cart)) {
        echo "✅ Article ajouté au panier<br>";
        echo "Contenu du panier: <pre>" . print_r($cart, true) . "</pre>";
    } else {
        echo "❌ Impossible d'ajouter au panier<br>";
    }
} else {
    echo "✅ Panier contient " . count($cart) . " article(s)<br>";
    echo "Contenu du panier: <pre>" . print_r($cart, true) . "</pre>";
}

// Test de processOrder
echo "<h2>5. Test de processOrder</h2>";
if (!empty($cart)) {
    echo "Tentative de traitement de commande...<br>";
    $orderId = $pos->processOrder('cash');
    
    if ($orderId) {
        echo "✅ Commande traitée avec succès, ID: $orderId<br>";
    } else {
        echo "❌ Échec du traitement de commande<br>";
    }
} else {
    echo "⚠️ Panier vide, impossible de tester processOrder<br>";
}

// Vérifier les logs d'erreur
echo "<h2>6. Logs d'erreur récents</h2>";
$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    $lines = file($errorLog);
    $recentLines = array_slice($lines, -20); // 20 dernières lignes
    echo "<pre>" . implode('', $recentLines) . "</pre>";
} else {
    echo "Fichier de log non trouvé ou vide<br>";
    echo "Vérifiez les logs du serveur web ou PHP<br>";
}

echo "<h2>7. Informations de session</h2>";
echo "Session ID: " . session_id() . "<br>";
echo "Contenu session: <pre>" . print_r($_SESSION, true) . "</pre>";

?>
