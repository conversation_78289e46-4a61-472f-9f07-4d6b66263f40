<?php
/**
 * Script pour aider à activer l'extension GD
 */

echo "<h1>🔧 Activation Extension GD</h1>";

echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>⚠️ Extension GD non disponible</h2>";
echo "<p>L'extension GD est nécessaire pour traiter les images. Voici comment l'activer :</p>";
echo "</div>";

echo "<h2>📋 Instructions détaillées</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>🎯 Méthode 1 : Via XAMPP Control Panel</h3>";
echo "<ol>";
echo "<li><strong>Ouvrez XAMPP Control Panel</strong></li>";
echo "<li><strong>Cliquez sur 'Config'</strong> à côté d'Apache</li>";
echo "<li><strong>Sélectionnez 'PHP (php.ini)'</strong></li>";
echo "<li><strong>Cherchez la ligne :</strong> <code>;extension=gd</code></li>";
echo "<li><strong>Supprimez le ;</strong> pour obtenir : <code>extension=gd</code></li>";
echo "<li><strong>Sauvegardez</strong> le fichier (Ctrl+S)</li>";
echo "<li><strong>Redémarrez Apache</strong> dans XAMPP</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>🎯 Méthode 2 : Localisation automatique du php.ini</h3>";

$phpIniPath = php_ini_loaded_file();
if ($phpIniPath) {
    echo "<p><strong>Fichier php.ini actuel :</strong></p>";
    echo "<code style='background: #f8f9fa; padding: 10px; display: block; border-radius: 5px;'>{$phpIniPath}</code>";
    
    if (file_exists($phpIniPath)) {
        echo "<p style='color: green;'>✅ Fichier trouvé et accessible</p>";
        
        // Lire le contenu pour vérifier GD
        $iniContent = file_get_contents($phpIniPath);
        
        if (strpos($iniContent, ';extension=gd') !== false) {
            echo "<p style='color: orange;'>⚠️ Extension GD trouvée mais commentée (;extension=gd)</p>";
            echo "<p><strong>Action :</strong> Supprimez le ; devant extension=gd</p>";
        } elseif (strpos($iniContent, 'extension=gd') !== false) {
            echo "<p style='color: blue;'>ℹ️ Extension GD déjà activée dans php.ini</p>";
            echo "<p><strong>Problème :</strong> Redémarrez Apache ou vérifiez les erreurs PHP</p>";
        } else {
            echo "<p style='color: red;'>❌ Ligne extension=gd non trouvée</p>";
            echo "<p><strong>Action :</strong> Ajoutez la ligne 'extension=gd' dans la section [PHP]</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Fichier php.ini non accessible</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Impossible de localiser php.ini</p>";
}
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>🎯 Méthode 3 : Vérification des extensions disponibles</h3>";

$loadedExtensions = get_loaded_extensions();
echo "<p><strong>Extensions PHP chargées :</strong> " . count($loadedExtensions) . "</p>";

$imageExtensions = ['gd', 'imagick', 'gmagick'];
$foundImageExt = [];

foreach ($imageExtensions as $ext) {
    if (in_array($ext, $loadedExtensions)) {
        $foundImageExt[] = $ext;
    }
}

if (!empty($foundImageExt)) {
    echo "<p style='color: green;'>✅ Extensions d'image trouvées : " . implode(', ', $foundImageExt) . "</p>";
} else {
    echo "<p style='color: red;'>❌ Aucune extension d'image trouvée</p>";
}

// Afficher quelques extensions importantes
$importantExt = ['gd', 'mysqli', 'pdo', 'curl', 'openssl', 'zip'];
echo "<h4>Extensions importantes :</h4>";
echo "<ul>";
foreach ($importantExt as $ext) {
    if (in_array($ext, $loadedExtensions)) {
        echo "<li style='color: green;'>✅ {$ext}</li>";
    } else {
        echo "<li style='color: red;'>❌ {$ext}</li>";
    }
}
echo "</ul>";
echo "</div>";

echo "<h2>🔄 Après activation</h2>";

echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>✅ Une fois GD activé :</h3>";
echo "<ol>";
echo "<li><strong>Redémarrez Apache</strong> dans XAMPP Control Panel</li>";
echo "<li><strong>Rafraîchissez cette page</strong> pour vérifier</li>";
echo "<li><strong>Visitez <a href='check_php_environment.php'>check_php_environment.php</a></strong> pour confirmer</li>";
echo "<li><strong>Lancez <a href='export.php'>export.php</a></strong> pour exporter vos images</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🆘 Si le problème persiste</h2>";

echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>❌ Solutions alternatives :</h3>";
echo "<ol>";
echo "<li><strong>Réinstallez XAMPP :</strong> Téléchargez la dernière version</li>";
echo "<li><strong>Vérifiez la version PHP :</strong> Certaines versions n'incluent pas GD</li>";
echo "<li><strong>Utilisez WampServer :</strong> Alternative à XAMPP avec GD inclus</li>";
echo "<li><strong>Serveur en ligne :</strong> Uploadez vos fichiers sur un hébergeur avec GD</li>";
echo "</ol>";

echo "<h4>🔍 Informations de débogage :</h4>";
echo "<ul>";
echo "<li><strong>Version PHP :</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Système :</strong> " . PHP_OS . "</li>";
echo "<li><strong>Architecture :</strong> " . (PHP_INT_SIZE * 8) . " bits</li>";
echo "<li><strong>SAPI :</strong> " . php_sapi_name() . "</li>";
echo "</ul>";
echo "</div>";

// Bouton de test
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='check_php_environment.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;'>";
echo "🔄 Tester à nouveau l'environnement";
echo "</a>";
echo "</div>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3; margin-top: 30px;'>";
echo "<h3>💡 Note importante</h3>";
echo "<p>L'extension GD est <strong>essentielle</strong> pour :</p>";
echo "<ul>";
echo "<li>Lire les images depuis la base HSQL</li>";
echo "<li>Convertir et réparer les images corrompues</li>";
echo "<li>Sauvegarder au format PNG</li>";
echo "<li>Redimensionner et optimiser les images</li>";
echo "</ul>";
echo "<p>Sans GD, l'export d'images est impossible.</p>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
h1, h2, h3 { color: #333; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
ol, ul { margin-left: 20px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
