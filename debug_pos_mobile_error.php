<?php
/**
 * Script pour capturer l'erreur exacte qui se produit dans pos_mobile.php
 */

// Activer l'affichage complet des erreurs
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Fonction pour capturer toutes les erreurs
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🚨 Erreur PHP capturée:</h4>";
    echo "<strong>Type:</strong> " . $errno . "<br>";
    echo "<strong>Message:</strong> " . htmlspecialchars($errstr) . "<br>";
    echo "<strong>Fichier:</strong> " . $errfile . "<br>";
    echo "<strong>Ligne:</strong> " . $errline . "<br>";
    echo "</div>";
    return true;
}

// Fonction pour capturer les exceptions non gérées
function customExceptionHandler($exception) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🚨 Exception capturée:</h4>";
    echo "<strong>Message:</strong> " . htmlspecialchars($exception->getMessage()) . "<br>";
    echo "<strong>Fichier:</strong> " . $exception->getFile() . "<br>";
    echo "<strong>Ligne:</strong> " . $exception->getLine() . "<br>";
    echo "<strong>Stack trace:</strong><br>";
    echo "<pre>" . htmlspecialchars($exception->getTraceAsString()) . "</pre>";
    echo "</div>";
}

// Activer les gestionnaires d'erreurs personnalisés
set_error_handler('customErrorHandler');
set_exception_handler('customExceptionHandler');

echo "<h1>🔍 Debug pos_mobile.php - Capture d'erreurs</h1>";

echo "<div style='background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>ℹ️ Ce script va exécuter le même code que pos_mobile.php étape par étape</h3>";
echo "<p>Toutes les erreurs seront capturées et affichées clairement.</p>";
echo "</div>";

// Démarrer la session comme dans pos_mobile.php
echo "<h2>1. Démarrage de session</h2>";
try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "✅ Session démarrée<br>";
} catch (Exception $e) {
    echo "❌ Erreur session: " . $e->getMessage() . "<br>";
}

// Inclure pos_config.php comme dans pos_mobile.php
echo "<h2>2. Inclusion de pos_config.php</h2>";
try {
    require_once 'pos_config.php';
    echo "✅ pos_config.php inclus<br>";
} catch (Exception $e) {
    echo "❌ Erreur inclusion: " . $e->getMessage() . "<br>";
    exit;
}

// Vérifier la connexion
echo "<h2>3. Vérification connexion POS</h2>";
try {
    if ($pos->isConnected()) {
        echo "✅ POS connecté<br>";
    } else {
        echo "❌ POS non connecté<br>";
        exit;
    }
} catch (Exception $e) {
    echo "❌ Erreur vérification connexion: " . $e->getMessage() . "<br>";
    exit;
}

// Simuler les actions POST si nécessaire
echo "<h2>4. Simulation actions POST</h2>";
$_SERVER['REQUEST_METHOD'] = 'GET'; // Forcer GET pour éviter les actions POST
echo "✅ Mode GET forcé (pas d'actions POST)<br>";

// Récupération des données comme dans pos_mobile.php
echo "<h2>5. Récupération des catégories</h2>";
try {
    $categories = $pos->getCategories();
    echo "✅ Catégories récupérées: " . count($categories) . "<br>";
} catch (Exception $e) {
    echo "❌ Erreur getCategories: " . $e->getMessage() . "<br>";
}

echo "<h2>6. Récupération des articles</h2>";
try {
    $selectedCategory = $_GET['category'] ?? '';
    $searchQuery = $_GET['search'] ?? '';
    
    echo "Paramètres: catégorie='" . htmlspecialchars($selectedCategory) . "', recherche='" . htmlspecialchars($searchQuery) . "'<br>";
    
    if ($searchQuery) {
        echo "Exécution de searchArticles...<br>";
        $articles = $pos->searchArticles($searchQuery);
    } else {
        echo "Exécution de getArticlesByCategory...<br>";
        $articles = $pos->getArticlesByCategory($selectedCategory ?: null);
    }
    echo "✅ Articles récupérés: " . count($articles) . "<br>";
} catch (Exception $e) {
    echo "❌ Erreur récupération articles: " . $e->getMessage() . "<br>";
    echo "Fichier: " . $e->getFile() . "<br>";
    echo "Ligne: " . $e->getLine() . "<br>";
}

echo "<h2>7. Récupération du panier</h2>";
try {
    $cart = $pos->getCart();
    echo "✅ Panier récupéré: " . count($cart) . " articles<br>";
} catch (Exception $e) {
    echo "❌ Erreur getCart: " . $e->getMessage() . "<br>";
}

echo "<h2>8. Calcul du total</h2>";
try {
    $cartTotal = $pos->getCartTotalTTC();
    echo "✅ Total calculé: " . $cartTotal . "<br>";
} catch (Exception $e) {
    echo "❌ Erreur getCartTotalTTC: " . $e->getMessage() . "<br>";
}

echo "<h2>9. Récupération des statistiques</h2>";
try {
    $todayStats = $pos->getTodayStats();
    echo "✅ Statistiques récupérées<br>";
} catch (Exception $e) {
    echo "❌ Erreur getTodayStats: " . $e->getMessage() . "<br>";
}

echo "<h2>10. Test des prix d'articles</h2>";
if (!empty($articles)) {
    try {
        $firstArticle = $articles[0];
        $articleId = $firstArticle['IDarticles'];
        echo "Test du prix pour l'article ID: " . $articleId . "<br>";
        
        $price = $pos->getArticlePrice($articleId);
        echo "✅ Prix récupéré: " . $price . "<br>";
    } catch (Exception $e) {
        echo "❌ Erreur getArticlePrice: " . $e->getMessage() . "<br>";
        echo "Fichier: " . $e->getFile() . "<br>";
        echo "Ligne: " . $e->getLine() . "<br>";
    }
}

echo "<h2>✅ Diagnostic terminé</h2>";
echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
echo "<h3>Résumé:</h3>";
echo "<p>Si aucune erreur n'apparaît ci-dessus, le problème pourrait être:</p>";
echo "<ul>";
echo "<li>Un problème de cache navigateur</li>";
echo "<li>Une erreur JavaScript côté client</li>";
echo "<li>Un problème avec les requêtes AJAX</li>";
echo "<li>Une erreur qui ne se produit que dans certaines conditions</li>";
echo "</ul>";
echo "<p><strong>Prochaine étape:</strong> Essayez d'accéder à pos_mobile.php avec ce lien:</p>";
echo "<p><a href='pos_mobile.php?debug=1&v=" . time() . "' target='_blank'>pos_mobile.php (mode debug)</a></p>";
echo "</div>";
?>
