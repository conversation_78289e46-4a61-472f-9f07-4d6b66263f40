<?php
/**
 * Debug spécifique pour la validation de commande
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'pos_config.php';

echo "<h1>Debug Validation de Commande</h1>";

$pos = new POSConfig();

// Test de connexion
echo "<h2>1. Test de connexion</h2>";
if ($pos->isConnected()) {
    echo "✅ Connexion réussie<br>";
} else {
    echo "❌ Échec de connexion<br>";
    exit;
}

// Vérifier le panier
echo "<h2>2. État du panier</h2>";
$cart = $pos->getCart();
echo "Nombre d'articles dans le panier: " . count($cart) . "<br>";

if (empty($cart)) {
    echo "⚠️ Panier vide, ajout d'un article de test...<br>";
    
    // Récupérer un article de test
    try {
        $sql = "SELECT TOP 1 IDarticle, designation, prix, IDCategorie, Cuisine FROM articles";
        $stmt = $pos->pdo->prepare($sql);
        $stmt->execute();
        $article = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($article) {
            echo "Article trouvé: " . $article['designation'] . " (ID: " . $article['IDarticle'] . ")<br>";
            $pos->addToCart($article['IDarticle'], 1);
            $cart = $pos->getCart();
            echo "Article ajouté au panier<br>";
        } else {
            echo "❌ Aucun article trouvé dans la base<br>";
            exit;
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la récupération d'un article: " . $e->getMessage() . "<br>";
        exit;
    }
}

echo "Contenu du panier:<br>";
foreach ($cart as $id => $item) {
    echo "- Article ID: $id, Nom: {$item['article']['designation']}, Qté: {$item['quantity']}, Prix: {$item['price']}<br>";
}

// Test des paramètres serveur/table
echo "<h2>3. Paramètres serveur/table</h2>";
$serverId = $pos->getCurrentServerId();
$tableId = $pos->getCurrentTableId();
echo "Serveur ID: $serverId<br>";
echo "Table ID: $tableId<br>";

// Test du calcul du total
echo "<h2>4. Calcul du total</h2>";
$total = $pos->getCartTotalTTC();
echo "Total TTC: $total<br>";

// Test de la structure des tables
echo "<h2>5. Vérification structure table tickets</h2>";
try {
    $sql = "SELECT MAX(NumTick) as last_num FROM tickets";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    $nextNumTick = ($result['last_num'] ?? 0) + 1;
    echo "Prochain NumTick: $nextNumTick<br>";
} catch (Exception $e) {
    echo "❌ Erreur lors de la lecture de tickets: " . $e->getMessage() . "<br>";
}

// Test d'insertion manuelle dans tickets
echo "<h2>6. Test d'insertion manuelle dans tickets</h2>";
try {
    $sql = "INSERT INTO tickets (
        NumTick, 
        IDServeur, 
        IDTables, 
        DATE, 
        heure, 
        total, 
        typepaye, 
        Djr, 
        ImpTick
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $testData = [
        99999,              // NumTick de test
        1,                  // IDServeur
        103,                // IDTables
        date('Ymd'),        // DATE
        date('His'),        // heure
        10.50,              // total
        'ESP',              // typepaye
        1,                  // Djr
        1                   // ImpTick
    ];
    
    echo "Données de test: <pre>" . print_r($testData, true) . "</pre>";
    
    $stmt = $pos->pdo->prepare($sql);
    $success = $stmt->execute($testData);
    
    if ($success) {
        $ticketId = $pos->pdo->lastInsertId();
        echo "✅ Insertion réussie, Ticket ID: $ticketId<br>";
        
        // Supprimer le ticket de test
        $sql = "DELETE FROM tickets WHERE IDtickets = ?";
        $stmt = $pos->pdo->prepare($sql);
        $stmt->execute([$ticketId]);
        echo "Ticket de test supprimé<br>";
    } else {
        $errorInfo = $stmt->errorInfo();
        echo "❌ Erreur d'insertion: " . print_r($errorInfo, true) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Exception lors de l'insertion: " . $e->getMessage() . "<br>";
}

// Test de processOrder avec debugging
echo "<h2>7. Test de processOrder</h2>";
if (!empty($cart)) {
    echo "Tentative de validation de commande...<br>";
    
    // Activer le debugging
    error_log("=== DÉBUT TEST PROCESSORDER ===");
    
    $orderId = $pos->processOrder('cash');
    
    if ($orderId) {
        echo "✅ Commande validée avec succès, ID: $orderId<br>";
    } else {
        echo "❌ Échec de la validation de commande<br>";
    }
    
    error_log("=== FIN TEST PROCESSORDER ===");
} else {
    echo "⚠️ Panier vide<br>";
}

// Afficher les derniers logs
echo "<h2>8. Logs d'erreur récents</h2>";
$logFile = ini_get('error_log');
if ($logFile && file_exists($logFile)) {
    $lines = file($logFile);
    $recentLines = array_slice($lines, -30); // 30 dernières lignes
    echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
    foreach ($recentLines as $line) {
        if (strpos($line, 'DEBUG:') !== false || strpos($line, 'Erreur') !== false) {
            echo htmlspecialchars($line);
        }
    }
    echo "</pre>";
} else {
    echo "Fichier de log non accessible<br>";
    echo "Vérifiez les logs dans: <br>";
    echo "- Apache: error.log<br>";
    echo "- PHP: php_errors.log<br>";
    echo "- Windows Event Viewer<br>";
}

?>
