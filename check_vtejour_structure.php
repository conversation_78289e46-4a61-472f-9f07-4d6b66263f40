<?php
/**
 * Vérifier la structure de la table VteJour pour identifier le problème
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Vérification structure table VteJour</h1>";

try {
    $pdo = new PDO('odbc:DataCafe', 'admin', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "✅ Connexion réussie<br>";
} catch (Exception $e) {
    echo "❌ Erreur connexion: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>1. Structure de la table VteJour</h2>";

try {
    $sql = "SELECT TOP 1 * FROM VteJour";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ Table VteJour accessible<br>";
        echo "<h3>Colonnes disponibles:</h3>";
        echo "<ul>";
        foreach (array_keys($result) as $column) {
            echo "<li><strong>$column</strong>";
            
            // Vérifier spécifiquement les colonnes liées aux articles
            if (strpos(strtolower($column), 'article') !== false) {
                echo " <span style='color: blue;'>← Colonne article</span>";
            }
            echo "</li>";
        }
        echo "</ul>";
        
        // Vérifier spécifiquement IDarticles vs IDarticle
        if (array_key_exists('IDarticles', $result)) {
            echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 5px;'>";
            echo "✅ La table VteJour utilise 'IDarticles' (correct)";
            echo "</div>";
        } elseif (array_key_exists('IDarticle', $result)) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;'>";
            echo "⚠️ La table VteJour utilise 'IDarticle' (sans s) - C'EST LE PROBLÈME !";
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px;'>";
            echo "⚠️ Aucune colonne IDarticle/IDarticles trouvée dans VteJour";
            echo "</div>";
        }
        
    } else {
        echo "⚠️ Table VteJour vide<br>";
        
        // Essayer de voir la structure même si vide
        try {
            $sql = "SELECT * FROM VteJour WHERE 1=0";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            
            echo "<h3>Structure de la table vide:</h3>";
            for ($i = 0; $i < $stmt->columnCount(); $i++) {
                $meta = $stmt->getColumnMeta($i);
                echo "- " . $meta['name'] . " (" . $meta['native_type'] . ")<br>";
            }
        } catch (Exception $e) {
            echo "❌ Impossible de récupérer la structure: " . $e->getMessage() . "<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erreur accès VteJour: " . $e->getMessage() . "<br>";
}

echo "<h2>2. Structure de la table articles (pour comparaison)</h2>";

try {
    $sql = "SELECT TOP 1 * FROM articles";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ Table articles accessible<br>";
        if (array_key_exists('IDarticles', $result)) {
            echo "✅ La table articles utilise 'IDarticles'<br>";
        } elseif (array_key_exists('IDarticle', $result)) {
            echo "⚠️ La table articles utilise 'IDarticle'<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erreur accès articles: " . $e->getMessage() . "<br>";
}

echo "<h2>3. Test de la requête problématique</h2>";

// Tester la requête qui pose problème dans processOrder
echo "<h3>3.1. Test avec IDarticles (supposé correct)</h3>";
try {
    $sql = "SELECT TOP 1 v.*, a.designation 
            FROM VteJour v 
            INNER JOIN articles a ON v.IDarticles = a.IDarticles";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ Requête avec IDarticles fonctionne<br>";
    } else {
        echo "⚠️ Requête avec IDarticles ne retourne aucun résultat<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur requête avec IDarticles: " . $e->getMessage() . "<br>";
}

echo "<h3>3.2. Test avec IDarticle (supposé incorrect)</h3>";
try {
    $sql = "SELECT TOP 1 v.*, a.designation 
            FROM VteJour v 
            INNER JOIN articles a ON v.IDarticle = a.IDarticles";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ Requête avec IDarticle fonctionne - La table VteJour utilise IDarticle !<br>";
    } else {
        echo "⚠️ Requête avec IDarticle ne retourne aucun résultat<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur requête avec IDarticle: " . $e->getMessage() . "<br>";
    echo "Message: " . htmlspecialchars($e->getMessage()) . "<br>";
}

echo "<h2>4. Solution recommandée</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;'>";
echo "<h3>📋 Analyse:</h3>";
echo "<p>Si la table VteJour utilise 'IDarticle' (sans s) mais que la table articles utilise 'IDarticles' (avec s), ";
echo "alors les requêtes de jointure dans processOrder doivent être corrigées.</p>";

echo "<h3>🔧 Correction nécessaire:</h3>";
echo "<p>Dans pos_config.php, ligne ~921, changer:</p>";
echo "<code>INNER JOIN articles a ON v.IDarticles = a.IDarticles</code><br>";
echo "<p>En:</p>";
echo "<code>INNER JOIN articles a ON v.IDarticle = a.IDarticles</code><br>";
echo "<p>(Si VteJour utilise IDarticle sans s)</p>";
echo "</div>";

echo "<h2>✅ Diagnostic terminé</h2>";
echo "<p>Vérifiez les résultats ci-dessus pour identifier le nom correct des colonnes.</p>";
?>
