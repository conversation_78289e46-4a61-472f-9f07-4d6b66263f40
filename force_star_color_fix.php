<?php
/**
 * Test forcé pour corriger la couleur des étoiles
 */

echo "<h1>🔧 Correction Forcée Couleur Étoiles</h1>";

echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>⚠️ Problème de cache détecté</h2>";
echo "<p>Les modifications CSS ne s'appliquent pas. Voici plusieurs solutions :</p>";
echo "</div>";

echo "<h2>🔧 Solutions de dépannage</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>1. Vider le cache du navigateur :</h3>";
echo "<ul>";
echo "<li><strong>Chrome/Edge :</strong> Ctrl + Shift + R (ou F12 → clic droit sur rafraîchir → 'Vider le cache et actualiser')</li>";
echo "<li><strong>Firefox :</strong> Ctrl + F5</li>";
echo "<li><strong>Safari :</strong> Cmd + Option + R</li>";
echo "</ul>";

echo "<h3>2. CSS renforcé appliqué :</h3>";
echo "<pre style='background: #e9ecef; padding: 15px; border-radius: 5px; font-size: 12px;'>";
echo "/* Règles CSS multiples pour forcer la couleur grise */\n";
echo ".category-icon {\n";
echo "    color: #9ca3af !important;\n";
echo "}\n\n";
echo ".category-btn .category-icon {\n";
echo "    color: #9ca3af !important;\n";
echo "}\n\n";
echo ".category-btn:hover .category-icon {\n";
echo "    color: #9ca3af !important;\n";
echo "}\n\n";
echo ".category-btn.active .category-icon {\n";
echo "    color: #9ca3af !important;\n";
echo "}";
echo "</pre>";
echo "</div>";

echo "<h2>🧪 Test en direct avec CSS inline</h2>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>Simulation avec CSS forcé :</h3>";

echo "<div style='background: white; border: 1px solid #ddd; border-radius: 8px; padding: 15px; max-width: 300px; margin: 20px 0;'>";
echo "<h4 style='margin-top: 0;'>Menu Catégories (CSS forcé)</h4>";

// Tous les articles (actif)
echo "<button style='display: flex; align-items: center; gap: 10px; padding: 12px 16px; border: none; border-radius: 8px; background: #3498db; color: #ffffff; cursor: pointer; width: 100%; margin-bottom: 8px;'>";
echo "<div style='font-size: 18px; min-width: 20px; color: #9ca3af !important;'>⭐</div>";
echo "<div style='font-weight: 600;'>Tous les articles</div>";
echo "</button>";

// Catégories normales
$sampleCategories = ['Boissons', 'Pâtisseries', 'Sandwichs', 'Salades'];
foreach ($sampleCategories as $cat) {
    echo "<button style='display: flex; align-items: center; gap: 10px; padding: 12px 16px; border: none; border-radius: 8px; background: #f8f9fa; color: #495057; cursor: pointer; width: 100%; margin-bottom: 8px;'>";
    echo "<div style='font-size: 18px; min-width: 20px; color: #9ca3af !important;'>⭐</div>";
    echo "<div style='font-weight: 600;'>{$cat}</div>";
    echo "</button>";
}

echo "</div>";

echo "<p><strong>Si les étoiles ci-dessus sont grises, le problème vient du cache de votre navigateur.</strong></p>";
echo "</div>";

echo "<h2>🎯 Test JavaScript pour forcer la couleur</h2>";

echo "<div style='background: #d1ecf1; color: #0c5460; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>Script JavaScript de correction :</h3>";
echo "<p>Ce script va forcer la couleur grise sur toutes les étoiles :</p>";

echo "<button onclick='forceStarColors()' style='background: #17a2b8; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 0;'>";
echo "🔧 Forcer la couleur grise des étoiles";
echo "</button>";

echo "<div id='jsResult' style='margin-top: 10px; padding: 10px; background: rgba(255,255,255,0.5); border-radius: 5px; display: none;'></div>";
echo "</div>";

echo "<h2>📋 Instructions étape par étape</h2>";

echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px;'>";
echo "<h3>✅ Pour résoudre le problème :</h3>";
echo "<ol>";
echo "<li><strong>Videz le cache :</strong> Ctrl + Shift + R (Chrome) ou Ctrl + F5 (Firefox)</li>";
echo "<li><strong>Visitez pos_mobile.php :</strong> <a href='pos_mobile.php?v=" . time() . "' target='_blank'>pos_mobile.php (cache forcé)</a></li>";
echo "<li><strong>Ouvrez le menu catégories</strong> et vérifiez les étoiles</li>";
echo "<li><strong>Si toujours pas gris :</strong> Utilisez le bouton JavaScript ci-dessus</li>";
echo "<li><strong>En dernier recours :</strong> Redémarrez votre navigateur</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🔍 Diagnostic avancé</h2>";

echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 8px;'>";
echo "<h3>Pour diagnostiquer le problème :</h3>";
echo "<ol>";
echo "<li><strong>Ouvrez pos_mobile.php</strong></li>";
echo "<li><strong>Appuyez sur F12</strong> pour ouvrir les outils développeur</li>";
echo "<li><strong>Allez dans l'onglet 'Elements'</strong></li>";
echo "<li><strong>Trouvez une étoile</strong> (div class='category-icon')</li>";
echo "<li><strong>Regardez les styles appliqués</strong> dans le panneau de droite</li>";
echo "<li><strong>Vérifiez si</strong> <code>color: #9ca3af !important</code> apparaît</li>";
echo "</ol>";

echo "<h4>Si le style n'apparaît pas :</h4>";
echo "<ul>";
echo "<li>Le fichier pos_mobile.php n'a pas été sauvegardé correctement</li>";
echo "<li>Il y a un cache serveur</li>";
echo "<li>Le navigateur utilise une version en cache</li>";
echo "</ul>";

echo "<h4>Si le style apparaît mais est barré :</h4>";
echo "<ul>";
echo "<li>Un autre style plus spécifique l'écrase</li>";
echo "<li>Il faut augmenter la spécificité CSS</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🚨 Solution d'urgence</h2>";

echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;'>";
echo "<h3>Si rien ne fonctionne :</h3>";
echo "<p>Copiez ce code CSS et ajoutez-le manuellement dans pos_mobile.php :</p>";

echo "<textarea style='width: 100%; height: 120px; font-family: monospace; font-size: 12px; padding: 10px; border: 1px solid #ccc; border-radius: 5px;' readonly>";
echo "/* CORRECTION FORCÉE COULEUR ÉTOILES */\n";
echo ".category-icon,\n";
echo ".category-btn .category-icon,\n";
echo ".category-btn:hover .category-icon,\n";
echo ".category-btn.active .category-icon {\n";
echo "    color: #9ca3af !important;\n";
echo "}\n\n";
echo "/* Alternative avec sélecteur universel */\n";
echo ".categories-sidebar * {\n";
echo "    color: #9ca3af !important;\n";
echo "}";
echo "</textarea>";

echo "<p><strong>Ajoutez ce code dans la section &lt;style&gt; de pos_mobile.php</strong></p>";
echo "</div>";
?>

<script>
function forceStarColors() {
    const resultDiv = document.getElementById('jsResult');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '⏳ Application de la couleur grise...';
    
    // Ouvrir pos_mobile.php dans une nouvelle fenêtre
    const newWindow = window.open('pos_mobile.php', '_blank');
    
    // Attendre que la page se charge puis appliquer les styles
    setTimeout(() => {
        try {
            // Injecter du CSS dans la nouvelle fenêtre
            const style = newWindow.document.createElement('style');
            style.textContent = `
                .category-icon,
                .category-btn .category-icon,
                .category-btn:hover .category-icon,
                .category-btn.active .category-icon {
                    color: #9ca3af !important;
                }
            `;
            newWindow.document.head.appendChild(style);
            
            resultDiv.innerHTML = '✅ Couleur grise appliquée ! Vérifiez la nouvelle fenêtre.';
            resultDiv.style.color = 'green';
        } catch (e) {
            resultDiv.innerHTML = '❌ Erreur : ' + e.message + '. Essayez de vider le cache manuellement.';
            resultDiv.style.color = 'red';
        }
    }, 2000);
}

// Test automatique au chargement
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Test couleur étoiles chargé');
    console.log('Couleur cible: #9ca3af (gris clair)');
    
    // Vérifier si on peut accéder à pos_mobile.php
    fetch('pos_mobile.php')
        .then(response => {
            if (response.ok) {
                console.log('✅ pos_mobile.php accessible');
            } else {
                console.log('⚠️ Problème d\'accès à pos_mobile.php');
            }
        })
        .catch(error => {
            console.log('❌ Erreur réseau:', error);
        });
});
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
h1, h2, h3, h4 { color: #333; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
pre { font-family: monospace; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
button:hover { opacity: 0.9; }
</style>
