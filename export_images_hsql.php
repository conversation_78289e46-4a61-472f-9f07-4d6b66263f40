<?php
/**
 * Export automatique de toutes les images HSQL vers le dossier images/articles/
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300); // 5 minutes max

echo "<h1>📤 Export Images HSQL → Fichiers</h1>";

require_once 'pos_config.php';

if (!$pos->isConnected()) {
    echo "<div style='color: red; font-size: 18px;'>❌ Erreur de connexion à la base HSQL</div>";
    exit;
}

// Créer le dossier de destination
$exportDir = 'images/articles';
if (!file_exists($exportDir)) {
    if (mkdir($exportDir, 0755, true)) {
        echo "<p style='color: green;'>✅ Dossier {$exportDir} créé</p>";
    } else {
        echo "<p style='color: red;'>❌ Impossible de créer le dossier {$exportDir}</p>";
        exit;
    }
} else {
    echo "<p style='color: blue;'>ℹ️ Dossier {$exportDir} existe déjà</p>";
}

echo "<h2>1. Recherche des articles avec images</h2>";

try {
    // Récupérer tous les articles avec images
    $sql = "SELECT IDarticles, designation, image FROM articles WHERE image IS NOT NULL ORDER BY IDarticles";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $articlesWithImages = $stmt->fetchAll();
    
    $totalArticles = count($articlesWithImages);
    echo "<p><strong>Articles avec images trouvés :</strong> {$totalArticles}</p>";
    
    if ($totalArticles === 0) {
        echo "<div style='color: orange; padding: 15px; background: #fff3cd; border-radius: 5px;'>";
        echo "<h3>⚠️ Aucun article avec image trouvé</h3>";
        echo "<p>Vérifiez que votre base HSQL contient des images dans le champ 'image' de la table 'articles'.</p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>2. Export en cours...</h2>";
    
    $exportStats = [
        'success' => 0,
        'errors' => 0,
        'skipped' => 0,
        'total' => $totalArticles
    ];
    
    echo "<div id='progress-container' style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<div id='progress-bar' style='background: #28a745; height: 20px; width: 0%; border-radius: 10px; transition: width 0.3s;'></div>";
    echo "<p id='progress-text'>Préparation...</p>";
    echo "</div>";
    
    echo "<div id='export-log' style='max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
    
    foreach ($articlesWithImages as $index => $article) {
        $articleId = $article['IDarticles'];
        $designation = $article['designation'];
        $imageData = $article['image'];
        
        echo "<div style='margin: 5px 0; padding: 5px; background: white; border-radius: 3px;'>";
        echo "<strong>Article {$articleId}:</strong> " . htmlspecialchars($designation) . "<br>";
        
        // Traiter les données image
        if (is_resource($imageData)) {
            $imageData = stream_get_contents($imageData);
        }
        
        if (empty($imageData)) {
            echo "<span style='color: orange;'>⚠️ Données image vides - ignoré</span>";
            $exportStats['skipped']++;
            echo "</div>";
            continue;
        }
        
        echo "Taille: " . strlen($imageData) . " octets - ";
        
        // Détecter le format d'image
        $imageFormat = null;
        $fileExtension = null;
        
        // Vérifier PNG
        if (substr($imageData, 0, 8) === "\x89PNG\r\n\x1a\n") {
            $imageFormat = 'PNG';
            $fileExtension = 'png';
        }
        // Vérifier JPEG
        elseif (substr($imageData, 0, 3) === "\xFF\xD8\xFF") {
            $imageFormat = 'JPEG';
            $fileExtension = 'jpg';
        }
        // Vérifier GIF
        elseif (substr($imageData, 0, 6) === "GIF87a" || substr($imageData, 0, 6) === "GIF89a") {
            $imageFormat = 'GIF';
            $fileExtension = 'gif';
        }
        // Essayer de détecter avec getimagesizefromstring
        else {
            $imageInfo = @getimagesizefromstring($imageData);
            if ($imageInfo) {
                switch ($imageInfo[2]) {
                    case IMAGETYPE_PNG:
                        $imageFormat = 'PNG';
                        $fileExtension = 'png';
                        break;
                    case IMAGETYPE_JPEG:
                        $imageFormat = 'JPEG';
                        $fileExtension = 'jpg';
                        break;
                    case IMAGETYPE_GIF:
                        $imageFormat = 'GIF';
                        $fileExtension = 'gif';
                        break;
                }
            }
        }
        
        if (!$imageFormat) {
            // Essayer de nettoyer et détecter
            $cleanedData = $imageData;
            
            // Chercher signature PNG dans les données
            $pngPos = strpos($imageData, "\x89PNG");
            if ($pngPos !== false) {
                $cleanedData = substr($imageData, $pngPos);
                $imageFormat = 'PNG';
                $fileExtension = 'png';
            }
            // Chercher signature JPEG
            else {
                $jpegPos = strpos($imageData, "\xFF\xD8");
                if ($jpegPos !== false) {
                    $cleanedData = substr($imageData, $jpegPos);
                    $imageFormat = 'JPEG';
                    $fileExtension = 'jpg';
                }
            }
            
            if ($imageFormat) {
                $imageData = $cleanedData;
                echo "Nettoyé et détecté comme {$imageFormat} - ";
            }
        }
        
        if (!$imageFormat) {
            echo "<span style='color: red;'>❌ Format non reconnu - ignoré</span>";
            $exportStats['errors']++;
            echo "</div>";
            continue;
        }
        
        echo "Format: {$imageFormat} - ";
        
        // Nom du fichier de destination
        $filename = "{$exportDir}/{$articleId}.{$fileExtension}";
        
        // Vérifier si le fichier existe déjà
        if (file_exists($filename)) {
            echo "<span style='color: blue;'>ℹ️ Fichier existe déjà - remplacé</span>";
        }
        
        // Sauvegarder le fichier
        if (file_put_contents($filename, $imageData)) {
            $fileSize = filesize($filename);
            echo "<span style='color: green;'>✅ Exporté ({$fileSize} octets) → {$filename}</span>";
            $exportStats['success']++;
        } else {
            echo "<span style='color: red;'>❌ Erreur d'écriture</span>";
            $exportStats['errors']++;
        }
        
        echo "</div>";
        
        // Mettre à jour la barre de progression
        $progress = (($index + 1) / $totalArticles) * 100;
        echo "<script>
            document.getElementById('progress-bar').style.width = '{$progress}%';
            document.getElementById('progress-text').textContent = 'Article " . ($index + 1) . "/{$totalArticles} - {$progress}%';
            document.getElementById('export-log').scrollTop = document.getElementById('export-log').scrollHeight;
        </script>";
        
        // Forcer l'affichage
        if (ob_get_level()) ob_flush();
        flush();
        
        // Petite pause pour éviter la surcharge
        usleep(100000); // 0.1 seconde
    }
    
    echo "</div>";
    
    echo "<h2>3. Résultats de l'export</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>📊 Statistiques :</h3>";
    echo "<ul style='font-size: 16px;'>";
    echo "<li><strong>Total articles traités :</strong> {$exportStats['total']}</li>";
    echo "<li style='color: green;'><strong>✅ Exportés avec succès :</strong> {$exportStats['success']}</li>";
    echo "<li style='color: red;'><strong>❌ Erreurs :</strong> {$exportStats['errors']}</li>";
    echo "<li style='color: orange;'><strong>⚠️ Ignorés :</strong> {$exportStats['skipped']}</li>";
    echo "</ul>";
    
    $successRate = $exportStats['total'] > 0 ? round(($exportStats['success'] / $exportStats['total']) * 100, 1) : 0;
    echo "<p><strong>Taux de réussite :</strong> {$successRate}%</p>";
    echo "</div>";
    
    if ($exportStats['success'] > 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 Export réussi !</h3>";
        echo "<p><strong>{$exportStats['success']} images</strong> ont été exportées dans le dossier <code>{$exportDir}/</code></p>";
        
        echo "<h4>📁 Fichiers créés :</h4>";
        $exportedFiles = glob("{$exportDir}/*");
        echo "<div style='max-height: 200px; overflow-y: auto; background: rgba(255,255,255,0.5); padding: 10px; border-radius: 5px;'>";
        foreach ($exportedFiles as $file) {
            $filename = basename($file);
            $filesize = filesize($file);
            echo "<p style='margin: 2px 0; font-family: monospace;'>📷 {$filename} ({$filesize} octets)</p>";
        }
        echo "</div>";
        
        echo "<h4>🚀 Prochaines étapes :</h4>";
        echo "<ol>";
        echo "<li><strong>Testez pos_mobile.php</strong> - les images devraient maintenant s'afficher</li>";
        echo "<li><strong>Vérifiez la console</strong> (F12) pour voir les logs de chargement</li>";
        echo "<li><strong>Les images sont maintenant indépendantes</strong> de la base HSQL</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    if ($exportStats['errors'] > 0 || $exportStats['skipped'] > 0) {
        echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>⚠️ Attention</h3>";
        if ($exportStats['errors'] > 0) {
            echo "<p><strong>{$exportStats['errors']} erreurs</strong> lors de l'export. Vérifiez les permissions du dossier.</p>";
        }
        if ($exportStats['skipped'] > 0) {
            echo "<p><strong>{$exportStats['skipped']} articles ignorés</strong> car les données image étaient vides ou corrompues.</p>";
        }
        echo "</div>";
    }
    
    echo "<h2>4. Test rapide</h2>";
    
    if ($exportStats['success'] > 0) {
        echo "<p>Voici un aperçu des premières images exportées :</p>";
        
        $testFiles = array_slice(glob("{$exportDir}/*"), 0, 6);
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;'>";
        
        foreach ($testFiles as $file) {
            $filename = basename($file);
            $articleId = pathinfo($filename, PATHINFO_FILENAME);
            
            echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 10px; text-align: center; background: white;'>";
            echo "<img src='{$file}' style='width: 100%; max-width: 120px; height: 80px; object-fit: cover; border-radius: 5px;' alt='Article {$articleId}'>";
            echo "<p style='margin: 5px 0; font-size: 12px;'><strong>ID:</strong> {$articleId}</p>";
            echo "<p style='margin: 0; font-size: 11px; color: #666;'>{$filename}</p>";
            echo "</div>";
        }
        
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; font-size: 18px; padding: 20px; background: #f8d7da; border-radius: 8px;'>";
    echo "<h3>❌ Erreur lors de l'export</h3>";
    echo "<p><strong>Message :</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre style='background: #fff; padding: 10px; border-radius: 5px; overflow-x: auto;'>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3; margin-top: 30px;'>";
echo "<h3>💡 Informations importantes</h3>";
echo "<ul>";
echo "<li><strong>Dossier de destination :</strong> <code>{$exportDir}/</code></li>";
echo "<li><strong>Format des noms :</strong> <code>IDarticle.extension</code> (ex: 123.png)</li>";
echo "<li><strong>Formats supportés :</strong> PNG, JPG, GIF</li>";
echo "<li><strong>Avantages :</strong> Performance, fiabilité, indépendance de la base</li>";
echo "</ul>";
echo "</div>";
?>

<script>
console.log('📤 Export images HSQL terminé');

// Compter les images exportées
document.addEventListener('DOMContentLoaded', function() {
    const exportedImages = document.querySelectorAll('img[src*="images/articles/"]');
    console.log(`📊 Images exportées visibles: ${exportedImages.length}`);
    
    exportedImages.forEach((img, index) => {
        img.addEventListener('load', function() {
            console.log(`✅ Image exportée ${index + 1} chargée:`, this.src);
        });
        
        img.addEventListener('error', function() {
            console.log(`❌ Erreur image exportée ${index + 1}:`, this.src);
        });
    });
});
</script>
