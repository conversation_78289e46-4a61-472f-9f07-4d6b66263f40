<?php
/**
 * Script de débogage pour identifier la source exacte de l'erreur SQL
 * "Rubrique IDarticle inconnue"
 */

// Activer l'affichage des erreurs
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Démarrer la session
session_start();

echo "<h1>🔍 Débogage erreur SQL IDarticle</h1>";

// Test 1: Connexion directe
echo "<h2>1. Test connexion directe</h2>";
try {
    $pdo = new PDO('odbc:DataCafe', 'admin', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "✅ Connexion PDO directe réussie<br>";
} catch (Exception $e) {
    echo "❌ Erreur connexion: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Vérifier la structure de la table articles
echo "<h2>2. Structure table articles</h2>";
try {
    $sql = "SELECT TOP 1 * FROM articles";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ Table articles accessible<br>";
        echo "Colonnes disponibles:<br>";
        foreach (array_keys($result) as $column) {
            echo "- " . $column . "<br>";
        }
        
        // Vérifier spécifiquement IDarticles
        if (array_key_exists('IDarticles', $result)) {
            echo "✅ Colonne 'IDarticles' trouvée<br>";
        } else {
            echo "❌ Colonne 'IDarticles' non trouvée<br>";
        }
        
        if (array_key_exists('IDarticle', $result)) {
            echo "⚠️ Colonne 'IDarticle' (sans s) trouvée<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erreur structure table: " . $e->getMessage() . "<br>";
}

// Test 3: Test requête avec IDarticles (correct)
echo "<h2>3. Test requête avec IDarticles</h2>";
try {
    $sql = "SELECT IDarticles, designation FROM articles WHERE IDarticles > 0 LIMIT 3";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll();
    echo "✅ Requête avec IDarticles réussie: " . count($results) . " résultats<br>";
} catch (Exception $e) {
    echo "❌ Erreur requête IDarticles: " . $e->getMessage() . "<br>";
}

// Test 4: Test requête avec IDarticle (incorrect) - pour voir l'erreur
echo "<h2>4. Test requête avec IDarticle (pour reproduire l'erreur)</h2>";
try {
    $sql = "SELECT IDarticle, designation FROM articles WHERE IDarticle > 0 LIMIT 3";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll();
    echo "⚠️ Requête avec IDarticle réussie (inattendu): " . count($results) . " résultats<br>";
} catch (Exception $e) {
    echo "✅ Erreur attendue avec IDarticle: " . $e->getMessage() . "<br>";
    echo "Code erreur: " . $e->getCode() . "<br>";
}

// Test 5: Charger pos_config.php et tester
echo "<h2>5. Test pos_config.php</h2>";
try {
    require_once 'pos_config.php';
    echo "✅ pos_config.php chargé<br>";
    
    if ($pos->isConnected()) {
        echo "✅ POSManager connecté<br>";
        
        // Test des méthodes principales
        $categories = $pos->getCategories();
        echo "✅ getCategories(): " . count($categories) . " catégories<br>";
        
        $articles = $pos->getArticlesByCategory();
        echo "✅ getArticlesByCategory(): " . count($articles) . " articles<br>";
        
    } else {
        echo "❌ POSManager non connecté<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur pos_config.php: " . $e->getMessage() . "<br>";
    echo "Fichier: " . $e->getFile() . "<br>";
    echo "Ligne: " . $e->getLine() . "<br>";
}

echo "<h2>📋 Résumé</h2>";
echo "<p>Ce script aide à identifier si l'erreur vient de:</p>";
echo "<ul>";
echo "<li>La structure de la base de données</li>";
echo "<li>Une requête utilisant le mauvais nom de champ</li>";
echo "<li>Un problème dans pos_config.php</li>";
echo "</ul>";
?>
