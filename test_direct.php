<?php
/**
 * Test direct sans passer par pos_config.php
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Test Direct de Validation</h1>";

// Configuration directe de la base
define('POS_DSN', 'odbc:DataCafe');
define('POS_USERNAME', 'admin');
define('POS_PASSWORD', '');

// Connexion directe
try {
    $pdo = new PDO(POS_DSN, POS_USERNAME, POS_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connexion directe réussie<br>";
} catch (Exception $e) {
    echo "❌ Erreur de connexion: " . $e->getMessage() . "<br>";
    exit;
}

// Test 1: Vérifier les tables
echo "<h2>📋 Test 1: Vérification des tables</h2>";

try {
    $sql = "SELECT COUNT(*) as count FROM tickets";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "✅ Table tickets: " . $result['count'] . " enregistrements<br>";
} catch (Exception $e) {
    echo "❌ Erreur table tickets: " . $e->getMessage() . "<br>";
    exit;
}

try {
    $sql = "SELECT COUNT(*) as count FROM VteJour";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "✅ Table VteJour: " . $result['count'] . " enregistrements<br>";
} catch (Exception $e) {
    echo "❌ Erreur table VteJour: " . $e->getMessage() . "<br>";
    exit;
}

try {
    $sql = "SELECT COUNT(*) as count FROM articles";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "✅ Table articles: " . $result['count'] . " enregistrements<br>";
} catch (Exception $e) {
    echo "❌ Erreur table articles: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Récupérer un article
echo "<h2>📦 Test 2: Récupération d'un article</h2>";

try {
    $sql = "SELECT TOP 1 IDarticles, designation, prix, IDCategorie, Cuisine FROM articles WHERE prix > 0";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $article = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($article) {
        echo "✅ Article trouvé:<br>";
        echo "- ID: " . $article['IDarticles'] . "<br>";
        echo "- Nom: " . $article['designation'] . "<br>";
        echo "- Prix: " . $article['prix'] . "<br>";
        echo "- Catégorie: " . $article['IDCategorie'] . "<br>";
        echo "- Cuisine: " . ($article['Cuisine'] ?? 'N/A') . "<br>";
    } else {
        echo "❌ Aucun article trouvé<br>";
        exit;
    }
} catch (Exception $e) {
    echo "❌ Erreur récupération article: " . $e->getMessage() . "<br>";
    exit;
}

// Test 3: Insertion d'un ticket de test
echo "<h2>🎫 Test 3: Insertion d'un ticket</h2>";

try {
    // Commencer une transaction
    $pdo->beginTransaction();
    echo "✅ Transaction démarrée<br>";
    
    // Récupérer le prochain NumTick
    $sql = "SELECT MAX(NumTick) as last_num FROM tickets";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    $nextNumTick = ($result['last_num'] ?? 0) + 1;
    echo "✅ Prochain NumTick: $nextNumTick<br>";
    
    // Préparer les données
    $dateNum = intval(date('Ymd'));
    $timeNum = intval(date('His'));
    $total = floatval($article['prix']);
    
    echo "Données à insérer:<br>";
    echo "- NumTick: $nextNumTick<br>";
    echo "- IDServeur: 1<br>";
    echo "- IDTables: 103<br>";
    echo "- DATE: $dateNum<br>";
    echo "- heure: $timeNum<br>";
    echo "- total: $total<br>";
    echo "- typepaye: ESP<br>";
    echo "- Djr: 1<br>";
    echo "- ImpTick: 1<br>";
    
    // Insérer le ticket
    $sql = "INSERT INTO tickets (NumTick, IDServeur, IDTables, DATE, heure, total, typepaye, Djr, ImpTick) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);
    
    $insertData = [
        intval($nextNumTick),
        1,
        103,
        $dateNum,
        $timeNum,
        $total,
        'ESP',
        1,
        1
    ];
    
    $success = $stmt->execute($insertData);
    
    if ($success) {
        echo "✅ Insertion ticket réussie<br>";
        
        // Récupérer l'ID du ticket
        $ticketId = $pdo->lastInsertId();
        
        if (!$ticketId) {
            // Fallback: récupérer par NumTick
            $sql = "SELECT IDtickets FROM tickets WHERE NumTick = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$nextNumTick]);
            $result = $stmt->fetch();
            $ticketId = $result['IDtickets'] ?? null;
            echo "✅ ID récupéré par NumTick: $ticketId<br>";
        } else {
            echo "✅ ID récupéré par lastInsertId: $ticketId<br>";
        }
        
        if ($ticketId) {
            // Insérer dans VteJour
            echo "<h2>📦 Test 4: Insertion VteJour</h2>";
            
            $sql = "INSERT INTO VteJour (articles, quantite, total, IDarticles, NumCategories, Cuisine, IDtickets) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $pdo->prepare($sql);

            $vteData = [
                $article['designation'],
                1,
                $total,
                $article['IDarticles'],
                $article['IDCategorie'],
                $article['Cuisine'] ?? '',
                $ticketId
            ];

            echo "Données VteJour à insérer:<br>";
            echo "- articles: " . $vteData[0] . "<br>";
            echo "- quantite: " . $vteData[1] . "<br>";
            echo "- total: " . $vteData[2] . "<br>";
            echo "- IDarticles: " . $vteData[3] . "<br>";
            echo "- NumCategories: " . $vteData[4] . "<br>";
            echo "- Cuisine: " . $vteData[5] . "<br>";
            echo "- IDtickets: " . $vteData[6] . "<br>";
            
            $success = $stmt->execute($vteData);
            
            if ($success) {
                echo "✅ Insertion VteJour réussie<br>";
                
                // Valider la transaction
                $pdo->commit();
                echo "✅ Transaction validée<br>";
                
                echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
                echo "<h2>🎉 SUCCÈS COMPLET!</h2>";
                echo "<p><strong>Ticket créé avec succès!</strong></p>";
                echo "<p>ID du ticket: <strong>$ticketId</strong></p>";
                echo "<p>NumTick: <strong>$nextNumTick</strong></p>";
                echo "<p>Total: <strong>$total</strong></p>";
                echo "</div>";
                
                // Vérifier en base
                echo "<h2>🔍 Vérification en base</h2>";
                
                $sql = "SELECT * FROM tickets WHERE IDtickets = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$ticketId]);
                $ticketVerif = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($ticketVerif) {
                    echo "✅ Ticket confirmé en base<br>";
                    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                    foreach ($ticketVerif as $key => $value) {
                        echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
                    }
                    echo "</table>";
                } else {
                    echo "❌ Ticket non trouvé en base<br>";
                }
                
                $sql = "SELECT * FROM VteJour WHERE IDtickets = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$ticketId]);
                $vteVerif = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($vteVerif)) {
                    echo "✅ VteJour confirmé en base (" . count($vteVerif) . " lignes)<br>";
                    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                    echo "<tr><th>Article</th><th>Quantité</th><th>Total</th><th>IDarticles</th></tr>";
                    foreach ($vteVerif as $detail) {
                        echo "<tr>";
                        echo "<td>{$detail['articles']}</td>";
                        echo "<td>{$detail['quantite']}</td>";
                        echo "<td>{$detail['total']}</td>";
                        echo "<td>{$detail['IDarticles']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "❌ VteJour non trouvé en base<br>";
                }
                
            } else {
                $errorInfo = $stmt->errorInfo();
                echo "❌ Erreur insertion VteJour:<br>";
                echo "SQLSTATE: " . $errorInfo[0] . "<br>";
                echo "Code: " . $errorInfo[1] . "<br>";
                echo "Message: " . $errorInfo[2] . "<br>";
                $pdo->rollBack();
            }
            
        } else {
            echo "❌ Impossible de récupérer l'ID du ticket<br>";
            $pdo->rollBack();
        }
        
    } else {
        $errorInfo = $stmt->errorInfo();
        echo "❌ Erreur insertion ticket:<br>";
        echo "SQLSTATE: " . $errorInfo[0] . "<br>";
        echo "Code: " . $errorInfo[1] . "<br>";
        echo "Message: " . $errorInfo[2] . "<br>";
        $pdo->rollBack();
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
}

echo "<h2>📊 Conclusion</h2>";
echo "<p>Si ce test fonctionne, le problème vient de la classe POSConfig ou de la gestion des sessions.</p>";
echo "<p>Si ce test échoue, le problème vient de la base de données ou des requêtes SQL.</p>";

?>
