<?php
/**
 * Test final pour vérifier que la validation de commande fonctionne
 * après correction du problème IDarticle/IDarticles
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Fonction pour capturer les erreurs
function finalTestErrorHandler($errno, $errstr, $errfile, $errline) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🚨 Erreur capturée:</h4>";
    echo "<strong>Message:</strong> " . htmlspecialchars($errstr) . "<br>";
    echo "<strong>Fichier:</strong> " . basename($errfile) . ":" . $errline . "<br>";
    echo "</div>";
    return true;
}

set_error_handler('finalTestErrorHandler');

echo "<h1>🎯 Test Final - Validation de Commande</h1>";

// Démarrer session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'pos_config.php';

echo "<h2>1. Vérifications préliminaires</h2>";
if ($pos->isConnected()) {
    echo "✅ Connexion POS OK<br>";
} else {
    echo "❌ Pas de connexion POS<br>";
    exit;
}

echo "<h2>2. Préparation du panier de test</h2>";
try {
    // Vider le panier
    $pos->clearCart();
    echo "✅ Panier vidé<br>";
    
    // Récupérer un article pour test
    $articles = $pos->getArticlesByCategory();
    if (empty($articles)) {
        echo "❌ Aucun article disponible<br>";
        exit;
    }
    
    $testArticle = $articles[0];
    $articleId = $testArticle['IDarticles'];
    echo "✅ Article de test sélectionné: {$testArticle['designation']} (ID: $articleId)<br>";
    
    // Ajouter au panier
    $result = $pos->addToCart($articleId, 1);
    if ($result === true || (is_array($result) && $result['success'])) {
        echo "✅ Article ajouté au panier<br>";
    } else {
        echo "❌ Impossible d'ajouter au panier<br>";
        if (is_array($result)) {
            echo "Erreur: " . $result['message'] . "<br>";
        }
        exit;
    }
    
    // Vérifier le panier
    $cart = $pos->getCart();
    echo "✅ Panier contient: " . count($cart) . " article(s)<br>";
    
    // Afficher le contenu du panier
    foreach ($cart as $id => $item) {
        echo "- Article: {$item['article']['designation']}, Quantité: {$item['quantity']}, Prix: {$item['price']}<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur préparation panier: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>3. Test de validation de commande</h2>";

try {
    echo "<h3>3.1. Avant validation</h3>";
    $cartBefore = $pos->getCart();
    $totalBefore = $pos->getCartTotalTTC();
    echo "Panier avant validation: " . count($cartBefore) . " articles<br>";
    echo "Total avant validation: $totalBefore €<br>";
    
    echo "<h3>3.2. Exécution de processOrder</h3>";
    echo "Tentative de validation avec méthode 'cash'...<br>";
    
    $orderId = $pos->processOrder('cash');
    
    if ($orderId) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🎉 SUCCÈS !</h4>";
        echo "<strong>Commande validée avec succès !</strong><br>";
        echo "<strong>ID de la commande:</strong> $orderId<br>";
        echo "</div>";
        
        echo "<h3>3.3. Vérifications post-validation</h3>";
        
        // Vérifier que le panier est vide
        $cartAfter = $pos->getCart();
        if (empty($cartAfter)) {
            echo "✅ Panier vidé après validation<br>";
        } else {
            echo "⚠️ Panier non vidé: " . count($cartAfter) . " articles restants<br>";
        }
        
        // Vérifier que le ticket existe en base
        try {
            $sql = "SELECT * FROM tickets WHERE IDtickets = ?";
            $stmt = $pos->pdo->prepare($sql);
            $stmt->execute([$orderId]);
            $ticket = $stmt->fetch();
            
            if ($ticket) {
                echo "✅ Ticket trouvé en base:<br>";
                echo "- NumTick: {$ticket['NumTick']}<br>";
                echo "- Total: {$ticket['total']}<br>";
                echo "- Date: {$ticket['DATE']}<br>";
                echo "- Heure: {$ticket['heure']}<br>";
            } else {
                echo "❌ Ticket non trouvé en base<br>";
            }
        } catch (Exception $e) {
            echo "❌ Erreur vérification ticket: " . $e->getMessage() . "<br>";
        }
        
        // Vérifier que les détails existent dans VteJour
        try {
            $sql = "SELECT * FROM VteJour WHERE IDtickets = ?";
            $stmt = $pos->pdo->prepare($sql);
            $stmt->execute([$orderId]);
            $details = $stmt->fetchAll();
            
            if (!empty($details)) {
                echo "✅ Détails trouvés dans VteJour: " . count($details) . " ligne(s)<br>";
                foreach ($details as $detail) {
                    echo "- Article: {$detail['articles']}, Quantité: {$detail['quantite']}, Total: {$detail['total']}<br>";
                }
            } else {
                echo "❌ Aucun détail trouvé dans VteJour<br>";
            }
        } catch (Exception $e) {
            echo "❌ Erreur vérification VteJour: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ ÉCHEC</h4>";
        echo "<strong>La validation de commande a échoué</strong><br>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Exception lors de la validation</h4>";
    echo "<strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>Fichier:</strong> " . $e->getFile() . "<br>";
    echo "<strong>Ligne:</strong> " . $e->getLine() . "<br>";
    echo "<strong>Stack trace:</strong><br>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}

echo "<h2>4. Test de pos_mobile.php</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;'>";
echo "<h3>🔗 Testez maintenant pos_mobile.php:</h3>";
$timestamp = time();
echo "<ul>";
echo "<li><a href='pos_mobile.php?v=$timestamp' target='_blank'>pos_mobile.php (normal)</a></li>";
echo "<li><a href='pos_mobile.php?debug=1&v=$timestamp' target='_blank'>pos_mobile.php (mode debug)</a></li>";
echo "</ul>";
echo "</div>";

echo "<h2>✅ Test final terminé</h2>";
echo "<p>Si la validation a réussi ci-dessus, le problème est résolu !</p>";
?>
