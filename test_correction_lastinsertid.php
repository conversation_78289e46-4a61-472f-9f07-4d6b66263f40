<?php
/**
 * Test de la correction lastInsertId
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Test Correction lastInsertId</h1>";

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'pos_config.php';

echo "<h2>1. Préparation du test</h2>";

try {
    // Vider le panier et ajouter un article
    $pos->clearCart();
    echo "✅ Panier vidé<br>";
    
    $articles = $pos->getArticlesByCategory();
    if (empty($articles)) {
        echo "❌ Aucun article disponible<br>";
        exit;
    }
    
    $testArticle = $articles[0];
    $result = $pos->addToCart($testArticle['IDarticles'], 1);
    
    if ($result === true || (is_array($result) && $result['success'])) {
        echo "✅ Article ajouté: {$testArticle['designation']} (ID: {$testArticle['IDarticles']})<br>";
    } else {
        echo "❌ Impossible d'ajouter l'article<br>";
        exit;
    }
    
    $cart = $pos->getCart();
    $total = $pos->getCartTotalTTC();
    echo "✅ Panier prêt: " . count($cart) . " article(s), Total: $total €<br>";
    
} catch (Exception $e) {
    echo "❌ Erreur préparation: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>2. Test de processOrder avec correction</h2>";

try {
    echo "Tentative de validation de commande...<br>";
    
    $orderId = $pos->processOrder('cash');
    
    if ($orderId) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🎉 SUCCÈS !</h3>";
        echo "<strong>Commande validée avec succès !</strong><br>";
        echo "<strong>ID de la commande:</strong> $orderId<br>";
        echo "</div>";
        
        // Vérifications post-validation
        echo "<h3>Vérifications:</h3>";
        
        // Panier vidé ?
        $cartAfter = $pos->getCart();
        if (empty($cartAfter)) {
            echo "✅ Panier vidé après validation<br>";
        } else {
            echo "⚠️ Panier non vidé: " . count($cartAfter) . " articles restants<br>";
        }
        
        // Ticket en base ?
        $sql = "SELECT * FROM tickets WHERE IDtickets = ?";
        $stmt = $pos->pdo->prepare($sql);
        $stmt->execute([$orderId]);
        $ticket = $stmt->fetch();
        
        if ($ticket) {
            echo "✅ Ticket trouvé en base:<br>";
            echo "- NumTick: {$ticket['NumTick']}<br>";
            echo "- Total: {$ticket['total']} €<br>";
            echo "- Date: {$ticket['DATE']}<br>";
            echo "- Serveur: {$ticket['IDServeur']}<br>";
            echo "- Table: {$ticket['IDTables']}<br>";
        } else {
            echo "❌ Ticket non trouvé en base<br>";
        }
        
        // Détails en VteJour ?
        $sql = "SELECT * FROM VteJour WHERE IDtickets = ?";
        $stmt = $pos->pdo->prepare($sql);
        $stmt->execute([$orderId]);
        $details = $stmt->fetchAll();
        
        if (!empty($details)) {
            echo "✅ Détails trouvés dans VteJour: " . count($details) . " ligne(s)<br>";
            foreach ($details as $detail) {
                echo "- {$detail['articles']}: {$detail['quantite']} × {$detail['total']} €<br>";
            }
        } else {
            echo "❌ Aucun détail trouvé dans VteJour<br>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ ÉCHEC</h3>";
        echo "<strong>La validation a encore échoué</strong><br>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Exception capturée</h3>";
    echo "<strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>Fichier:</strong> " . $e->getFile() . "<br>";
    echo "<strong>Ligne:</strong> " . $e->getLine() . "<br>";
    echo "</div>";
}

echo "<h2>3. Test de pos_mobile.php</h2>";

if (isset($orderId) && $orderId) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Correction réussie !</h3>";
    echo "<p>La validation de commande fonctionne maintenant. Vous pouvez tester pos_mobile.php :</p>";
    $timestamp = time();
    echo "<p><a href='pos_mobile.php?v=$timestamp' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Tester pos_mobile.php</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚠️ Problème persistant</h3>";
    echo "<p>La validation échoue encore. Vérifiez les logs d'erreur pour plus de détails.</p>";
    echo "</div>";
}

echo "<h2>4. Vérification des logs récents</h2>";

// Forcer un nouveau test avec logging
error_log("=== TEST CORRECTION LASTINSERTID - " . date('Y-m-d H:i:s') . " ===");

try {
    $pos->clearCart();
    $pos->addToCart($testArticle['IDarticles'], 1);
    error_log("DEBUG CORRECTION: Panier préparé");
    
    $result = $pos->processOrder('cash');
    error_log("DEBUG CORRECTION: processOrder résultat: " . ($result ? "Succès ID: $result" : "Échec"));
    
} catch (Exception $e) {
    error_log("DEBUG CORRECTION: Exception: " . $e->getMessage());
}

error_log("=== FIN TEST CORRECTION ===");

echo "<p>✅ Nouveau test enregistré dans les logs</p>";

echo "<h2>✅ Test terminé</h2>";
echo "<p>Si vous voyez 'SUCCÈS' ci-dessus, le problème est résolu !</p>";
?>
