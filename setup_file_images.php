<?php
/**
 * Configuration d'un système d'images basé sur des fichiers
 * Solution de contournement pour les problèmes HSQL
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Configuration Système Images Fichiers</h1>";

// Créer le dossier images s'il n'existe pas
$imageDir = 'images/articles';
if (!file_exists($imageDir)) {
    if (mkdir($imageDir, 0755, true)) {
        echo "<p style='color: green;'>✅ Dossier {$imageDir} créé</p>";
    } else {
        echo "<p style='color: red;'>❌ Impossible de créer le dossier {$imageDir}</p>";
        exit;
    }
} else {
    echo "<p style='color: blue;'>ℹ️ Dossier {$imageDir} existe déjà</p>";
}

// Créer quelques images de test
echo "<h2>1. Création d'images de test</h2>";

require_once 'pos_config.php';

if (!$pos->isConnected()) {
    echo "<p style='color: red;'>❌ Pas de connexion à la base</p>";
    exit;
}

// Récupérer quelques articles
$articles = $pos->getArticlesByCategory(null, true);
$testArticles = array_slice($articles, 0, 5);

echo "<p>Création d'images de test pour " . count($testArticles) . " articles...</p>";

foreach ($testArticles as $article) {
    $articleId = $article['IDarticles'];
    $designation = $article['designation'];
    
    // Créer une image de test avec GD
    $img = imagecreate(300, 200);
    
    // Couleurs
    $bg = imagecolorallocate($img, 240, 248, 255); // Bleu clair
    $border = imagecolorallocate($img, 70, 130, 180); // Bleu acier
    $text = imagecolorallocate($img, 25, 25, 112); // Bleu foncé
    
    // Bordure
    imagerectangle($img, 0, 0, 299, 199, $border);
    imagerectangle($img, 1, 1, 298, 198, $border);
    
    // Texte
    $lines = [
        "Article ID: {$articleId}",
        wordwrap($designation, 25, "\n"),
        "Prix: " . formatPrice($pos->getArticlePrice($articleId)),
        "Stock: " . $article['quantite']
    ];
    
    $y = 30;
    foreach ($lines as $line) {
        $sublines = explode("\n", $line);
        foreach ($sublines as $subline) {
            if (!empty(trim($subline))) {
                imagestring($img, 3, 10, $y, trim($subline), $text);
                $y += 20;
            }
        }
        $y += 10;
    }
    
    // Sauvegarder l'image
    $filename = "{$imageDir}/{$articleId}.png";
    if (imagepng($img, $filename)) {
        echo "<p style='color: green;'>✅ Image créée: {$filename}</p>";
    } else {
        echo "<p style='color: red;'>❌ Erreur création: {$filename}</p>";
    }
    
    imagedestroy($img);
}

echo "<h2>2. Test du système d'images fichiers</h2>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;'>";

foreach ($testArticles as $article) {
    $articleId = $article['IDarticles'];
    $imagePath = "{$imageDir}/{$articleId}.png";
    
    echo "<div style='border: 1px solid #ccc; padding: 15px; border-radius: 8px; background: white;'>";
    echo "<h4>" . htmlspecialchars($article['designation']) . "</h4>";
    echo "<p><strong>ID:</strong> {$articleId}</p>";
    
    if (file_exists($imagePath)) {
        echo "<p style='color: green;'>✅ Image fichier existe</p>";
        echo "<img src='{$imagePath}' style='width: 100%; max-width: 200px; border: 1px solid #ddd;' alt='Article {$articleId}'>";
    } else {
        echo "<p style='color: red;'>❌ Image fichier manquante</p>";
    }
    
    echo "</div>";
}

echo "</div>";

echo "<h2>3. Modification de pos_config.php</h2>";

// Lire le contenu actuel de pos_config.php
$configContent = file_get_contents('pos_config.php');

// Vérifier si les méthodes fichiers existent déjà
if (strpos($configContent, 'getArticleImageFile') === false) {
    echo "<p style='color: blue;'>Ajout des méthodes de gestion d'images fichiers...</p>";
    
    // Code à ajouter
    $newMethods = '
    /**
     * Vérifier si un article a une image fichier
     */
    public function hasArticleImageFile($articleId) {
        $imagePaths = [
            "images/articles/{$articleId}.png",
            "images/articles/{$articleId}.jpg",
            "images/articles/{$articleId}.jpeg",
            "images/articles/{$articleId}.gif"
        ];
        
        foreach ($imagePaths as $path) {
            if (file_exists($path)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Obtenir le chemin de l\'image fichier d\'un article
     */
    public function getArticleImageFile($articleId) {
        $imagePaths = [
            "images/articles/{$articleId}.png",
            "images/articles/{$articleId}.jpg", 
            "images/articles/{$articleId}.jpeg",
            "images/articles/{$articleId}.gif"
        ];
        
        foreach ($imagePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }
        
        return null;
    }
    
    /**
     * Méthode hybride: essaie fichier puis base de données
     */
    public function getArticleImageHybrid($articleId) {
        // Essayer d\'abord le fichier
        $filePath = $this->getArticleImageFile($articleId);
        if ($filePath) {
            return $filePath;
        }
        
        // Sinon essayer la base de données
        return $this->getArticleImageUrl($articleId);
    }
    
    /**
     * Vérifier si un article a une image (fichier ou base)
     */
    public function hasArticleImageHybrid($articleId) {
        return $this->hasArticleImageFile($articleId) || $this->hasArticleImage($articleId);
    }';
    
    // Ajouter avant la dernière accolade
    $lastBrace = strrpos($configContent, '}');
    $newContent = substr($configContent, 0, $lastBrace) . $newMethods . "\n" . substr($configContent, $lastBrace);
    
    if (file_put_contents('pos_config.php', $newContent)) {
        echo "<p style='color: green;'>✅ Méthodes ajoutées à pos_config.php</p>";
    } else {
        echo "<p style='color: red;'>❌ Erreur lors de la modification de pos_config.php</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ Méthodes fichiers déjà présentes dans pos_config.php</p>";
}

echo "<h2>4. Instructions d'utilisation</h2>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;'>";
echo "<h3>🎯 Comment utiliser le système fichiers :</h3>";
echo "<ol>";
echo "<li><strong>Ajoutez vos images</strong> dans le dossier <code>images/articles/</code></li>";
echo "<li><strong>Nommez-les</strong> avec l'ID de l'article : <code>123.png</code>, <code>456.jpg</code>, etc.</li>";
echo "<li><strong>Utilisez les nouvelles méthodes</strong> dans votre code :</li>";
echo "</ol>";

echo "<h4>Code d'exemple :</h4>";
echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
echo htmlspecialchars('<?php
// Vérifier si un article a une image
if ($pos->hasArticleImageHybrid($articleId)) {
    $imagePath = $pos->getArticleImageHybrid($articleId);
    echo "<img src=\'{$imagePath}\' alt=\'Article\'>";
} else {
    echo "<div class=\'no-image\'>Pas d\'image</div>";
}
?>');
echo "</pre>";
echo "</div>";

echo "<h2>5. Modification de pos_mobile.php</h2>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;'>";
echo "<h3>⚠️ Prochaine étape :</h3>";
echo "<p>Je vais maintenant modifier <code>pos_mobile.php</code> pour utiliser le système fichiers.</p>";
echo "<p>Cela garantira que les images s'affichent immédiatement !</p>";
echo "</div>";

echo "<h2>6. Test final</h2>";

// Tester les nouvelles méthodes
echo "<p>Test des nouvelles méthodes :</p>";

// Recharger la classe
require_once 'pos_config.php';

foreach ($testArticles as $article) {
    $articleId = $article['IDarticles'];
    
    echo "<div style='margin: 10px 0; padding: 10px; background: #f9f9f9; border-radius: 5px;'>";
    echo "<strong>Article {$articleId}:</strong> ";
    
    if (method_exists($pos, 'hasArticleImageHybrid')) {
        if ($pos->hasArticleImageHybrid($articleId)) {
            $imagePath = $pos->getArticleImageHybrid($articleId);
            echo "<span style='color: green;'>✅ Image trouvée: {$imagePath}</span>";
        } else {
            echo "<span style='color: orange;'>⚠️ Pas d'image</span>";
        }
    } else {
        echo "<span style='color: blue;'>ℹ️ Méthodes en cours d'ajout...</span>";
    }
    
    echo "</div>";
}

echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin-top: 30px;'>";
echo "<h3>🎉 Système fichiers configuré !</h3>";
echo "<p><strong>Avantages :</strong></p>";
echo "<ul>";
echo "<li>✅ Images garanties de fonctionner</li>";
echo "<li>✅ Pas de problème de base de données</li>";
echo "<li>✅ Performance optimale</li>";
echo "<li>✅ Facile à gérer</li>";
echo "</ul>";
echo "<p><strong>Prochaine étape :</strong> Modifier pos_mobile.php pour utiliser ce système</p>";
echo "</div>";
?>

<script>
console.log('🔧 Configuration système fichiers terminée');

document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img');
    let loadedCount = 0;
    
    images.forEach(img => {
        img.addEventListener('load', function() {
            loadedCount++;
            console.log(`✅ Image fichier ${loadedCount} chargée:`, this.src);
        });
        
        img.addEventListener('error', function() {
            console.log(`❌ Erreur image fichier:`, this.src);
        });
    });
    
    setTimeout(() => {
        console.log(`📊 Total images fichiers chargées: ${loadedCount}/${images.length}`);
        if (loadedCount === images.length && images.length > 0) {
            console.log('🎉 SUCCÈS: Toutes les images fichiers fonctionnent !');
        }
    }, 3000);
});
</script>
