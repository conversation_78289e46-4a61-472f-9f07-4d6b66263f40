<?php
/**
 * Vérification de l'environnement PHP pour l'export d'images
 */

echo "<h1>🔍 Vérification Environnement PHP</h1>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";

echo "<h2>📋 Informations PHP</h2>";

echo "<h3>Version PHP :</h3>";
echo "<p><strong>" . PHP_VERSION . "</strong></p>";

if (version_compare(PHP_VERSION, '5.4.0', '>=')) {
    echo "<p style='color: green;'>✅ Version PHP compatible (5.4+)</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Version PHP ancienne - certaines fonctions peuvent ne pas être disponibles</p>";
}

echo "<h3>Extensions requises :</h3>";

// Vérifier GD
if (extension_loaded('gd')) {
    echo "<p style='color: green;'>✅ Extension GD installée</p>";
    
    $gdInfo = gd_info();
    echo "<div style='margin-left: 20px; background: #e9ecef; padding: 10px; border-radius: 5px;'>";
    echo "<p><strong>Version GD :</strong> " . $gdInfo['GD Version'] . "</p>";
    
    echo "<p><strong>Formats supportés :</strong></p>";
    echo "<ul>";
    if ($gdInfo['PNG Support']) echo "<li style='color: green;'>✅ PNG</li>";
    else echo "<li style='color: red;'>❌ PNG</li>";
    
    if ($gdInfo['JPEG Support']) echo "<li style='color: green;'>✅ JPEG</li>";
    else echo "<li style='color: red;'>❌ JPEG</li>";
    
    if ($gdInfo['GIF Read Support']) echo "<li style='color: green;'>✅ GIF (lecture)</li>";
    else echo "<li style='color: red;'>❌ GIF (lecture)</li>";
    
    if ($gdInfo['GIF Create Support']) echo "<li style='color: green;'>✅ GIF (création)</li>";
    else echo "<li style='color: orange;'>⚠️ GIF (création)</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<p style='color: red;'>❌ Extension GD non installée</p>";
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Comment installer GD :</h4>";
    echo "<ul>";
    echo "<li><strong>XAMPP :</strong> GD est normalement inclus par défaut</li>";
    echo "<li><strong>Windows :</strong> Décommentez extension=gd dans php.ini</li>";
    echo "<li><strong>Linux :</strong> sudo apt-get install php-gd</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h3>Fonctions critiques :</h3>";

$functions = [
    'imagecreatefromstring' => 'Création d\'image depuis données binaires (PHP 5.4+)',
    'imagecreatefrompng' => 'Lecture PNG',
    'imagecreatefromjpeg' => 'Lecture JPEG',
    'imagecreatefromgif' => 'Lecture GIF',
    'imagepng' => 'Sauvegarde PNG',
    'imagedestroy' => 'Libération mémoire',
    'getimagesize' => 'Détection format image',
    'tempnam' => 'Fichiers temporaires',
    'file_put_contents' => 'Écriture fichier',
    'file_get_contents' => 'Lecture fichier'
];

echo "<ul>";
foreach ($functions as $function => $description) {
    if (function_exists($function)) {
        echo "<li style='color: green;'>✅ <strong>{$function}()</strong> - {$description}</li>";
    } else {
        echo "<li style='color: red;'>❌ <strong>{$function}()</strong> - {$description}</li>";
    }
}
echo "</ul>";

echo "<h3>Limites PHP :</h3>";

echo "<ul>";
echo "<li><strong>Mémoire :</strong> " . ini_get('memory_limit') . "</li>";
echo "<li><strong>Temps d'exécution :</strong> " . ini_get('max_execution_time') . " secondes</li>";
echo "<li><strong>Taille upload :</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>Taille POST :</strong> " . ini_get('post_max_size') . "</li>";
echo "</ul>";

// Recommandations
$memoryLimit = ini_get('memory_limit');
$memoryBytes = return_bytes($memoryLimit);
if ($memoryBytes < 128 * 1024 * 1024) { // 128MB
    echo "<p style='color: orange;'>⚠️ Mémoire limitée ({$memoryLimit}) - augmentez à 128M+ pour traiter de grandes images</p>";
} else {
    echo "<p style='color: green;'>✅ Mémoire suffisante ({$memoryLimit})</p>";
}

$execTime = ini_get('max_execution_time');
if ($execTime > 0 && $execTime < 300) { // 5 minutes
    echo "<p style='color: orange;'>⚠️ Temps d'exécution limité ({$execTime}s) - augmentez à 300s+ pour traiter beaucoup d'images</p>";
} else {
    echo "<p style='color: green;'>✅ Temps d'exécution suffisant</p>";
}

echo "</div>";

echo "<h2>🧪 Test des fonctions d'image</h2>";

if (extension_loaded('gd')) {
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    
    // Créer une image de test
    echo "<h3>Test de création d'image :</h3>";
    
    $testImage = imagecreate(100, 50);
    if ($testImage) {
        $bg = imagecolorallocate($testImage, 240, 248, 255);
        $text = imagecolorallocate($testImage, 0, 0, 0);
        imagestring($testImage, 3, 10, 15, 'TEST OK', $text);
        
        // Sauvegarder en PNG
        $testFile = 'test_image.png';
        if (imagepng($testImage, $testFile)) {
            echo "<p style='color: green;'>✅ Création et sauvegarde PNG réussies</p>";
            echo "<img src='{$testFile}' alt='Test image' style='border: 1px solid #ccc;'>";
            
            // Test de lecture
            if (function_exists('imagecreatefromstring')) {
                $testData = file_get_contents($testFile);
                $reloadedImage = imagecreatefromstring($testData);
                if ($reloadedImage) {
                    echo "<p style='color: green;'>✅ imagecreatefromstring() fonctionne</p>";
                    imagedestroy($reloadedImage);
                } else {
                    echo "<p style='color: red;'>❌ imagecreatefromstring() échoue</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ imagecreatefromstring() non disponible - utilisation de méthode alternative</p>";
                
                // Test méthode alternative
                $reloadedImage = imagecreatefrompng($testFile);
                if ($reloadedImage) {
                    echo "<p style='color: green;'>✅ imagecreatefrompng() fonctionne (méthode alternative)</p>";
                    imagedestroy($reloadedImage);
                } else {
                    echo "<p style='color: red;'>❌ imagecreatefrompng() échoue</p>";
                }
            }
            
            // Nettoyer
            unlink($testFile);
        } else {
            echo "<p style='color: red;'>❌ Erreur sauvegarde PNG</p>";
        }
        
        imagedestroy($testImage);
    } else {
        echo "<p style='color: red;'>❌ Impossible de créer une image de test</p>";
    }
    
    echo "</div>";
}

echo "<h2>📊 Conclusion</h2>";

$canExport = extension_loaded('gd') && 
             (function_exists('imagecreatefromstring') || function_exists('imagecreatefrompng')) &&
             function_exists('imagepng');

if ($canExport) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px;'>";
    echo "<h3>✅ Environnement compatible</h3>";
    echo "<p>Votre serveur peut exporter les images HSQL !</p>";
    echo "<p><strong>Prochaines étapes :</strong></p>";
    echo "<ol>";
    echo "<li><a href='test_export.php' target='_blank'>Testez les conditions d'export</a></li>";
    echo "<li><a href='export.php' target='_blank'>Lancez l'export des images</a></li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;'>";
    echo "<h3>❌ Environnement incompatible</h3>";
    echo "<p>Des éléments manquent pour l'export d'images :</p>";
    echo "<ul>";
    if (!extension_loaded('gd')) {
        echo "<li>Extension GD manquante</li>";
    }
    if (!function_exists('imagepng')) {
        echo "<li>Fonction imagepng() manquante</li>";
    }
    echo "</ul>";
    echo "<p><strong>Contactez votre administrateur système pour installer les extensions manquantes.</strong></p>";
    echo "</div>";
}

// Fonction helper
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
h1, h2, h3 { color: #333; }
ul, ol { margin-left: 20px; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
