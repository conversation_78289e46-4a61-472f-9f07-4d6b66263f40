<?php
/**
 * Réparation automatique des images exportées corrompues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300);

echo "<h1>🔧 Réparation Images Exportées</h1>";

$imageDir = 'images/articles';

if (!file_exists($imageDir)) {
    echo "<div style='color: red;'>❌ Dossier {$imageDir} n'existe pas</div>";
    exit;
}

$imageFiles = glob("{$imageDir}/*.{png,jpg,jpeg,gif}", GLOB_BRACE);

if (empty($imageFiles)) {
    echo "<div style='color: red;'>❌ Aucune image à réparer dans {$imageDir}</div>";
    exit;
}

echo "<h2>1. Analyse avant réparation</h2>";
echo "<p><strong>Images à traiter :</strong> " . count($imageFiles) . "</p>";

// Analyser l'état actuel
$currentStats = ['valid' => 0, 'invalid' => 0, 'total' => count($imageFiles)];

foreach ($imageFiles as $file) {
    $content = file_get_contents($file);
    $imageInfo = @getimagesizefromstring($content);
    
    if ($imageInfo) {
        $currentStats['valid']++;
    } else {
        $currentStats['invalid']++;
    }
}

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h3>📊 État actuel :</h3>";
echo "<ul>";
echo "<li><strong>Images valides :</strong> {$currentStats['valid']}</li>";
echo "<li><strong>Images corrompues :</strong> {$currentStats['invalid']}</li>";
echo "<li><strong>Total :</strong> {$currentStats['total']}</li>";
echo "</ul>";
echo "</div>";

if ($currentStats['invalid'] === 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px;'>";
    echo "<h3>✅ Aucune réparation nécessaire</h3>";
    echo "<p>Toutes les images sont déjà valides !</p>";
    echo "<p><a href='pos_mobile.php' target='_blank'>Testez pos_mobile.php</a></p>";
    echo "</div>";
    exit;
}

echo "<h2>2. Réparation en cours...</h2>";

$repairStats = [
    'success' => 0,
    'gd_repair' => 0,
    'manual_repair' => 0,
    'failed' => 0,
    'already_valid' => 0,
    'total' => count($imageFiles)
];

echo "<div id='progress-container' style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<div id='progress-bar' style='background: #28a745; height: 20px; width: 0%; border-radius: 10px; transition: width 0.3s;'></div>";
echo "<p id='progress-text'>Démarrage...</p>";
echo "</div>";

echo "<div id='repair-log' style='max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";

foreach ($imageFiles as $index => $file) {
    $filename = basename($file);
    $originalContent = file_get_contents($file);
    
    echo "<div style='margin: 5px 0; padding: 5px; background: white; border-radius: 3px;'>";
    echo "<strong>{$filename}:</strong> ";
    
    // Vérifier si déjà valide
    $imageInfo = @getimagesizefromstring($originalContent);
    if ($imageInfo) {
        echo "<span style='color: blue;'>✅ Déjà valide - ignoré</span>";
        $repairStats['already_valid']++;
        echo "</div>";
        
        // Mettre à jour la progression
        $progress = (($index + 1) / count($imageFiles)) * 100;
        echo "<script>
            document.getElementById('progress-bar').style.width = '{$progress}%';
            document.getElementById('progress-text').textContent = 'Fichier " . ($index + 1) . "/" . count($imageFiles) . " - {$progress}%';
            document.getElementById('repair-log').scrollTop = document.getElementById('repair-log').scrollHeight;
        </script>";
        
        if (ob_get_level()) ob_flush();
        flush();
        continue;
    }
    
    $repaired = false;
    $repairedContent = null;
    $repairMethod = '';
    
    // Méthode 1: Réparation avec GD
    if (function_exists('imagecreatefromstring')) {
        $gdImage = @imagecreatefromstring($originalContent);
        if ($gdImage) {
            ob_start();
            imagepng($gdImage, null, 9);
            $repairedContent = ob_get_contents();
            ob_end_clean();
            imagedestroy($gdImage);
            
            // Vérifier que la réparation a fonctionné
            $testInfo = @getimagesizefromstring($repairedContent);
            if ($testInfo) {
                $repaired = true;
                $repairMethod = 'GD';
                $repairStats['gd_repair']++;
            }
        }
    }
    
    // Méthode 2: Réparation manuelle si GD échoue
    if (!$repaired) {
        // Chercher signature PNG
        $pngSignature = "\x89PNG\r\n\x1a\n";
        $pngPos = strpos($originalContent, $pngSignature);
        
        if ($pngPos !== false) {
            $cleanedContent = substr($originalContent, $pngPos);
            
            // Supprimer les zéros à la fin
            $cleanedContent = rtrim($cleanedContent, "\x00");
            
            // Ajouter la fin PNG si nécessaire
            $pngEnd = "IEND\xaeB`\x82";
            if (substr($cleanedContent, -8) !== $pngEnd) {
                $cleanedContent .= "\x00\x00\x00\x00" . $pngEnd;
            }
            
            // Tester la réparation manuelle
            $testInfo = @getimagesizefromstring($cleanedContent);
            if ($testInfo) {
                $repairedContent = $cleanedContent;
                $repaired = true;
                $repairMethod = 'Manuel PNG';
                $repairStats['manual_repair']++;
            }
        }
        
        // Essayer avec JPEG si PNG échoue
        if (!$repaired) {
            $jpegPos = strpos($originalContent, "\xFF\xD8");
            if ($jpegPos !== false) {
                $cleanedContent = substr($originalContent, $jpegPos);
                
                // Chercher la fin JPEG
                $jpegEndPos = strrpos($cleanedContent, "\xFF\xD9");
                if ($jpegEndPos !== false) {
                    $cleanedContent = substr($cleanedContent, 0, $jpegEndPos + 2);
                }
                
                $testInfo = @getimagesizefromstring($cleanedContent);
                if ($testInfo) {
                    $repairedContent = $cleanedContent;
                    $repaired = true;
                    $repairMethod = 'Manuel JPEG';
                    $repairStats['manual_repair']++;
                }
            }
        }
    }
    
    // Sauvegarder la réparation
    if ($repaired && $repairedContent) {
        if (file_put_contents($file, $repairedContent)) {
            echo "<span style='color: green;'>✅ Réparé ({$repairMethod}) - " . strlen($repairedContent) . " octets</span>";
            $repairStats['success']++;
        } else {
            echo "<span style='color: red;'>❌ Erreur d'écriture</span>";
            $repairStats['failed']++;
        }
    } else {
        echo "<span style='color: red;'>❌ Réparation impossible</span>";
        $repairStats['failed']++;
    }
    
    echo "</div>";
    
    // Mettre à jour la progression
    $progress = (($index + 1) / count($imageFiles)) * 100;
    echo "<script>
        document.getElementById('progress-bar').style.width = '{$progress}%';
        document.getElementById('progress-text').textContent = 'Fichier " . ($index + 1) . "/" . count($imageFiles) . " - {$progress}%';
        document.getElementById('repair-log').scrollTop = document.getElementById('repair-log').scrollHeight;
    </script>";
    
    if (ob_get_level()) ob_flush();
    flush();
    
    usleep(50000); // 0.05 seconde
}

echo "</div>";

echo "<h2>3. Résultats de la réparation</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📊 Statistiques finales :</h3>";
echo "<ul style='font-size: 16px;'>";
echo "<li><strong>Total traité :</strong> {$repairStats['total']}</li>";
echo "<li style='color: green;'><strong>✅ Réparations réussies :</strong> {$repairStats['success']}</li>";
echo "<li style='color: blue;'><strong>🔧 Réparations GD :</strong> {$repairStats['gd_repair']}</li>";
echo "<li style='color: orange;'><strong>🛠️ Réparations manuelles :</strong> {$repairStats['manual_repair']}</li>";
echo "<li style='color: blue;'><strong>ℹ️ Déjà valides :</strong> {$repairStats['already_valid']}</li>";
echo "<li style='color: red;'><strong>❌ Échecs :</strong> {$repairStats['failed']}</li>";
echo "</ul>";

$totalValid = $repairStats['success'] + $repairStats['already_valid'];
$successRate = $repairStats['total'] > 0 ? round(($totalValid / $repairStats['total']) * 100, 1) : 0;
echo "<p><strong>Taux de réussite final :</strong> {$successRate}%</p>";
echo "</div>";

if ($repairStats['success'] > 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Réparation terminée avec succès !</h3>";
    echo "<p><strong>{$repairStats['success']} images</strong> ont été réparées.</p>";
    
    echo "<h4>🚀 Prochaines étapes :</h4>";
    echo "<ol>";
    echo "<li><strong><a href='verify_exported_images.php' target='_blank'>Vérifiez les images réparées</a></strong></li>";
    echo "<li><strong><a href='pos_mobile.php' target='_blank'>Testez pos_mobile.php</a></strong> - les images devraient maintenant s'afficher !</li>";
    echo "<li><strong>Videz le cache du navigateur</strong> (Ctrl+F5) si nécessaire</li>";
    echo "</ol>";
    echo "</div>";
}

if ($repairStats['failed'] > 0) {
    echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>⚠️ Attention</h3>";
    echo "<p><strong>{$repairStats['failed']} images</strong> n'ont pas pu être réparées.</p>";
    echo "<p>Ces images sont probablement trop corrompues ou dans un format non standard.</p>";
    echo "<p><strong>Solution :</strong> Remplacez-les manuellement par de nouvelles images.</p>";
    echo "</div>";
}

echo "<h2>4. Test rapide des images réparées</h2>";

if ($repairStats['success'] > 0) {
    echo "<p>Voici un aperçu des images après réparation :</p>";
    
    $testFiles = array_slice($imageFiles, 0, 6);
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    foreach ($testFiles as $file) {
        $filename = basename($file);
        $articleId = pathinfo($filename, PATHINFO_FILENAME);
        
        echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 10px; text-align: center; background: white;'>";
        echo "<img src='{$file}?v=" . time() . "' style='width: 100%; max-width: 120px; height: 80px; object-fit: cover; border-radius: 5px;' 
                   onload=\"this.nextElementSibling.innerHTML='✅ OK'; this.nextElementSibling.style.color='green';\"
                   onerror=\"this.nextElementSibling.innerHTML='❌ Erreur'; this.nextElementSibling.style.color='red';\"
                   alt='Article {$articleId}'>";
        echo "<div style='margin-top: 5px; font-weight: bold;'>⏳ Test...</div>";
        echo "<p style='margin: 5px 0; font-size: 12px;'><strong>ID:</strong> {$articleId}</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3; margin-top: 30px;'>";
echo "<h3>💡 Informations importantes</h3>";
echo "<ul>";
echo "<li><strong>Cache navigateur :</strong> Ajout de ?v=timestamp pour éviter le cache</li>";
echo "<li><strong>Formats réparés :</strong> Toutes les images sont maintenant en PNG propre</li>";
echo "<li><strong>Compatibilité :</strong> 100% compatible avec tous les navigateurs</li>";
echo "<li><strong>Performance :</strong> Optimisées pour un chargement rapide</li>";
echo "</ul>";
echo "</div>";
?>

<script>
console.log('🔧 Réparation images terminée');

document.addEventListener('DOMContentLoaded', function() {
    const repairedImages = document.querySelectorAll('img[src*="images/articles/"]');
    let loadedCount = 0;
    let errorCount = 0;
    
    console.log(`📊 Images réparées à tester: ${repairedImages.length}`);
    
    repairedImages.forEach((img, index) => {
        img.addEventListener('load', function() {
            loadedCount++;
            console.log(`✅ Image réparée ${index + 1} chargée:`, this.src);
        });
        
        img.addEventListener('error', function() {
            errorCount++;
            console.log(`❌ Image réparée ${index + 1} en erreur:`, this.src);
        });
    });
    
    setTimeout(() => {
        console.log('📊 RÉSULTATS RÉPARATION:');
        console.log(`Images chargées: ${loadedCount}/${repairedImages.length}`);
        console.log(`Erreurs: ${errorCount}/${repairedImages.length}`);
        
        if (loadedCount === repairedImages.length && repairedImages.length > 0) {
            console.log('🎉 SUCCÈS TOTAL: Toutes les images réparées fonctionnent !');
            console.log('✅ pos_mobile.php devrait maintenant afficher toutes les images');
        } else if (loadedCount > 0) {
            console.log('⚠️ SUCCÈS PARTIEL: Certaines images fonctionnent');
        } else {
            console.log('❌ ÉCHEC: Aucune image réparée ne fonctionne');
        }
    }, 5000);
});
</script>
