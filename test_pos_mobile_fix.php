<?php
/**
 * Test pour vérifier que pos_mobile.php fonctionne correctement
 * après les corrections des noms de champs
 */

require_once 'pos_config.php';

echo "<h1>🔧 Test des corrections pos_mobile.php</h1>";

// Test 1: Connexion
echo "<h2>1. Test de connexion</h2>";
if ($pos->isConnected()) {
    echo "✅ Connexion réussie<br>";
} else {
    echo "❌ Pas de connexion<br>";
    exit;
}

// Test 2: Récupération des catégories
echo "<h2>2. Test getCategories()</h2>";
try {
    $categories = $pos->getCategories();
    echo "✅ Catégories récupérées: " . count($categories) . " catégories<br>";
    if (!empty($categories)) {
        echo "Première catégorie: " . $categories[0]['categories'] . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur getCategories: " . $e->getMessage() . "<br>";
}

// Test 3: Récupération des articles
echo "<h2>3. Test getArticlesByCategory()</h2>";
try {
    $articles = $pos->getArticlesByCategory();
    echo "✅ Articles récupérés: " . count($articles) . " articles<br>";
    if (!empty($articles)) {
        $firstArticle = $articles[0];
        echo "Premier article:<br>";
        echo "- ID: " . $firstArticle['IDarticles'] . "<br>";
        echo "- Nom: " . $firstArticle['designation'] . "<br>";
        echo "- Prix: " . ($firstArticle['prix'] ?? 'N/A') . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Erreur getArticlesByCategory: " . $e->getMessage() . "<br>";
}

// Test 4: Test searchArticles
echo "<h2>4. Test searchArticles()</h2>";
try {
    $searchResults = $pos->searchArticles('a'); // Recherche simple
    echo "✅ Recherche réussie: " . count($searchResults) . " résultats<br>";
} catch (Exception $e) {
    echo "❌ Erreur searchArticles: " . $e->getMessage() . "<br>";
}

// Test 5: Test getArticlePrice
echo "<h2>5. Test getArticlePrice()</h2>";
if (!empty($articles)) {
    try {
        $firstArticleId = $articles[0]['IDarticles'];
        $price = $pos->getArticlePrice($firstArticleId);
        echo "✅ Prix récupéré pour l'article {$firstArticleId}: {$price}<br>";
    } catch (Exception $e) {
        echo "❌ Erreur getArticlePrice: " . $e->getMessage() . "<br>";
    }
}

// Test 6: Test du panier
echo "<h2>6. Test du panier</h2>";
try {
    $pos->clearCart();
    echo "✅ Panier vidé<br>";
    
    if (!empty($articles)) {
        $testArticleId = $articles[0]['IDarticles'];
        $result = $pos->addToCart($testArticleId, 1);
        if ($result === true || (is_array($result) && $result['success'])) {
            echo "✅ Article ajouté au panier<br>";
            
            $cart = $pos->getCart();
            echo "Contenu du panier: " . count($cart) . " article(s)<br>";
            
            $pos->clearCart();
            echo "✅ Panier vidé après test<br>";
        } else {
            echo "❌ Impossible d'ajouter au panier<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erreur test panier: " . $e->getMessage() . "<br>";
}

echo "<h2>✅ Tests terminés</h2>";
echo "<p>Si tous les tests sont verts, pos_mobile.php devrait fonctionner correctement.</p>";
echo "<p><a href='pos_mobile.php'>🔗 Tester pos_mobile.php</a></p>";
?>
