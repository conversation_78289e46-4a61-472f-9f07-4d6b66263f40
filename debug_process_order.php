<?php
/**
 * Debug spécifique pour la validation de commande (processOrder)
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Fonction pour capturer les erreurs
function orderErrorHandler($errno, $errstr, $errfile, $errline) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🚨 Erreur capturée:</h4>";
    echo "<strong>Message:</strong> " . htmlspecialchars($errstr) . "<br>";
    echo "<strong>Fichier:</strong> " . basename($errfile) . ":" . $errline . "<br>";
    echo "</div>";
    return true;
}

set_error_handler('orderErrorHandler');

echo "<h1>🔍 Debug Validation de Commande</h1>";

// Démarrer session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'pos_config.php';

echo "<h2>1. Vérifications préliminaires</h2>";
if ($pos->isConnected()) {
    echo "✅ Connexion POS OK<br>";
} else {
    echo "❌ Pas de connexion POS<br>";
    exit;
}

echo "<h2>2. Préparation du panier de test</h2>";
try {
    // Vider le panier
    $pos->clearCart();
    echo "✅ Panier vidé<br>";
    
    // Récupérer un article pour test
    $articles = $pos->getArticlesByCategory();
    if (empty($articles)) {
        echo "❌ Aucun article disponible<br>";
        exit;
    }
    
    $testArticle = $articles[0];
    $articleId = $testArticle['IDarticles'];
    echo "Article de test: {$testArticle['designation']} (ID: $articleId)<br>";
    
    // Ajouter au panier
    $result = $pos->addToCart($articleId, 1);
    if ($result === true || (is_array($result) && $result['success'])) {
        echo "✅ Article ajouté au panier<br>";
    } else {
        echo "❌ Impossible d'ajouter au panier<br>";
        if (is_array($result)) {
            echo "Erreur: " . $result['message'] . "<br>";
        }
        exit;
    }
    
    // Vérifier le panier
    $cart = $pos->getCart();
    echo "Panier contient: " . count($cart) . " article(s)<br>";
    
} catch (Exception $e) {
    echo "❌ Erreur préparation panier: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>3. Analyse de la méthode processOrder</h2>";

// Regarder le code de processOrder pour identifier les requêtes SQL
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<h3>🔍 Recherche des requêtes SQL dans processOrder</h3>";

$posConfigContent = file_get_contents('pos_config.php');

// Extraire la méthode processOrder
$startPos = strpos($posConfigContent, 'function processOrder');
if ($startPos !== false) {
    $endPos = strpos($posConfigContent, 'function ', $startPos + 1);
    if ($endPos === false) {
        $endPos = strpos($posConfigContent, '}', $startPos);
        // Trouver la vraie fin de la fonction
        $braceCount = 0;
        $pos = $startPos;
        while ($pos < strlen($posConfigContent)) {
            if ($posConfigContent[$pos] === '{') $braceCount++;
            if ($posConfigContent[$pos] === '}') $braceCount--;
            if ($braceCount === 0 && $pos > $startPos + 100) {
                $endPos = $pos;
                break;
            }
            $pos++;
        }
    }
    
    $processOrderCode = substr($posConfigContent, $startPos, $endPos - $startPos);
    
    // Chercher les requêtes SQL
    preg_match_all('/\$sql\s*=\s*["\']([^"\']+)["\']/', $processOrderCode, $matches);
    
    if (!empty($matches[1])) {
        echo "<h4>Requêtes SQL trouvées dans processOrder:</h4>";
        foreach ($matches[1] as $i => $query) {
            echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border-left: 3px solid #007bff;'>";
            echo "<strong>Requête " . ($i + 1) . ":</strong><br>";
            echo "<code>" . htmlspecialchars($query) . "</code><br>";
            
            // Vérifier si elle contient IDarticle
            if (strpos($query, 'IDarticle') !== false && strpos($query, 'IDarticles') === false) {
                echo "<span style='color: red;'>⚠️ PROBLÈME: Utilise 'IDarticle' au lieu de 'IDarticles'</span>";
            } else if (strpos($query, 'IDarticles') !== false) {
                echo "<span style='color: green;'>✅ Utilise correctement 'IDarticles'</span>";
            }
            echo "</div>";
        }
    } else {
        echo "Aucune requête SQL trouvée dans processOrder<br>";
    }
} else {
    echo "Méthode processOrder non trouvée<br>";
}
echo "</div>";

echo "<h2>4. Test de processOrder étape par étape</h2>";

try {
    echo "<h3>4.1. Avant processOrder</h3>";
    $cartBefore = $pos->getCart();
    echo "Panier avant: " . count($cartBefore) . " articles<br>";
    foreach ($cartBefore as $id => $item) {
        echo "- Article ID: $id, Nom: {$item['article']['designation']}, Qté: {$item['quantity']}<br>";
    }
    
    echo "<h3>4.2. Exécution de processOrder</h3>";
    echo "Tentative de validation avec méthode 'cash'...<br>";
    
    $orderId = $pos->processOrder('cash');
    
    if ($orderId) {
        echo "✅ Commande validée avec succès!<br>";
        echo "ID de la commande: $orderId<br>";
    } else {
        echo "❌ Échec de la validation de commande<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Exception lors de processOrder:<br>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>Fichier:</strong> " . $e->getFile() . "<br>";
    echo "<strong>Ligne:</strong> " . $e->getLine() . "<br>";
    echo "<strong>Stack trace:</strong><br>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}

echo "<h2>5. Vérification des tables</h2>";

// Vérifier les structures des tables utilisées par processOrder
$tables = ['tickets', 'VteJour'];

foreach ($tables as $table) {
    try {
        echo "<h3>Table: $table</h3>";
        $sql = "SELECT TOP 1 * FROM $table";
        $stmt = $pos->pdo->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result) {
            echo "✅ Table accessible, colonnes: " . implode(', ', array_keys($result)) . "<br>";
        } else {
            echo "⚠️ Table vide mais accessible<br>";
        }
    } catch (Exception $e) {
        echo "❌ Erreur table $table: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>✅ Debug terminé</h2>";
echo "<p>Vérifiez les erreurs capturées ci-dessus pour identifier le problème exact.</p>";
?>
