<?php
/**
 * Script pour détecter et corriger les conflits de sessions
 */

echo "<h1>🔧 Correction des conflits de sessions</h1>";

echo "<h2>1. Recherche des appels session_start()</h2>";

$phpFiles = glob('*.php');
$sessionFiles = [];

foreach ($phpFiles as $file) {
    $content = file_get_contents($file);
    $lines = explode("\n", $content);
    
    foreach ($lines as $lineNum => $line) {
        if (strpos($line, 'session_start()') !== false) {
            $sessionFiles[$file][] = [
                'line' => $lineNum + 1,
                'content' => trim($line),
                'safe' => strpos($line, 'session_status()') !== false
            ];
        }
    }
}

if (!empty($sessionFiles)) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Fichiers avec session_start() trouvés:</h3>";
    
    foreach ($sessionFiles as $file => $occurrences) {
        echo "<h4>$file:</h4>";
        echo "<ul>";
        foreach ($occurrences as $occurrence) {
            $status = $occurrence['safe'] ? '✅ Sécurisé' : '⚠️ À corriger';
            echo "<li>Ligne {$occurrence['line']}: <code>{$occurrence['content']}</code> - $status</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
} else {
    echo "✅ Aucun appel session_start() trouvé<br>";
}

echo "<h2>2. Correction automatique</h2>";

$corrected = 0;
foreach ($sessionFiles as $file => $occurrences) {
    $content = file_get_contents($file);
    $modified = false;
    
    foreach ($occurrences as $occurrence) {
        if (!$occurrence['safe']) {
            // Remplacer session_start() par la version sécurisée
            $oldPattern = 'session_start();';
            $newPattern = 'if (session_status() === PHP_SESSION_NONE) {
    session_start();
}';
            
            if (strpos($content, $oldPattern) !== false) {
                $content = str_replace($oldPattern, $newPattern, $content);
                $modified = true;
                $corrected++;
            }
        }
    }
    
    if ($modified) {
        file_put_contents($file, $content);
        echo "✅ Corrigé: $file<br>";
    }
}

if ($corrected > 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Corrections appliquées</h3>";
    echo "<p>$corrected fichier(s) corrigé(s) pour éviter les conflits de sessions.</p>";
    echo "</div>";
} else {
    echo "ℹ️ Aucune correction nécessaire<br>";
}

echo "<h2>3. Test de pos_mobile.php</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<h3>🔗 Testez maintenant pos_mobile.php:</h3>";
$timestamp = time();
echo "<ul>";
echo "<li><a href='pos_mobile.php?debug=1&v=$timestamp' target='_blank'>pos_mobile.php (mode debug)</a></li>";
echo "<li><a href='pos_mobile.php?v=$timestamp' target='_blank'>pos_mobile.php (normal)</a></li>";
echo "</ul>";
echo "</div>";

echo "<h2>4. Vérification finale</h2>";

// Test rapide
try {
    // Démarrer une session de test
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "✅ Session de test démarrée sans erreur<br>";
    
    // Tester pos_config.php
    require_once 'pos_config.php';
    echo "✅ pos_config.php chargé sans erreur de session<br>";
    
    if ($pos->isConnected()) {
        echo "✅ POS connecté<br>";
    } else {
        echo "❌ POS non connecté<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur lors du test: " . $e->getMessage() . "<br>";
}

echo "<h2>✅ Correction des sessions terminée</h2>";
echo "<p>Les conflits de sessions ont été corrigés. Testez maintenant pos_mobile.php.</p>";
?>
