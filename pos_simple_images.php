<?php
/**
 * Interface POS simple avec images HSQL
 * Utilise la méthode directe qui fonctionne : SELECT image FROM articles
 */

require_once 'pos_config.php';

// Récupérer les articles
$articles = $pos->getArticlesByCategory(null, true);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BeCoffe POS - Interface Simple</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        
        .header { 
            background: linear-gradient(135deg, #6f4e37, #8b4513); 
            color: white; 
            padding: 20px; 
            text-align: center; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        
        .status { 
            padding: 15px; 
            border-radius: 8px; 
            margin-bottom: 20px; 
            text-align: center; 
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        .products-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); 
            gap: 20px; 
            margin-top: 20px;
        }
        
        .product-card { 
            background: white; 
            border-radius: 12px; 
            overflow: hidden; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
            transition: transform 0.2s, box-shadow 0.2s;
            border: 1px solid #e9ecef;
        }
        
        .product-card:hover { 
            transform: translateY(-4px); 
            box-shadow: 0 8px 25px rgba(0,0,0,0.15); 
        }
        
        .product-image { 
            width: 100%; 
            height: 180px; 
            object-fit: cover; 
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .no-image { 
            height: 180px; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            background: linear-gradient(45deg, #f8f9fa, #e9ecef); 
            color: #6c757d; 
            font-size: 14px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .product-info { padding: 20px; }
        
        .product-name { 
            font-size: 16px; 
            font-weight: 600; 
            color: #2c3e50; 
            margin-bottom: 8px; 
            line-height: 1.3;
        }
        
        .product-category { 
            background: #6f4e37; 
            color: white; 
            padding: 4px 8px; 
            border-radius: 12px; 
            font-size: 11px; 
            display: inline-block; 
            margin-bottom: 10px;
        }
        
        .product-price { 
            font-size: 20px; 
            font-weight: bold; 
            color: #8b4513; 
            margin-bottom: 8px; 
        }
        
        .product-stock { 
            font-size: 13px; 
            color: #6c757d; 
            margin-bottom: 15px; 
        }
        
        .stock-low { color: #ffc107; }
        .stock-out { color: #dc3545; }
        .stock-ok { color: #28a745; }
        
        .add-btn { 
            width: 100%; 
            padding: 12px; 
            background: #28a745; 
            color: white; 
            border: none; 
            border-radius: 8px; 
            font-size: 14px; 
            font-weight: 600; 
            cursor: pointer; 
            transition: background 0.2s;
        }
        
        .add-btn:hover:not(:disabled) { background: #218838; }
        .add-btn:disabled { background: #6c757d; cursor: not-allowed; }
        
        .cart-summary { 
            position: fixed; 
            top: 20px; 
            right: 20px; 
            background: white; 
            padding: 20px; 
            border-radius: 12px; 
            box-shadow: 0 4px 20px rgba(0,0,0,0.15); 
            min-width: 250px;
            z-index: 1000;
        }
        
        .cart-title { 
            font-size: 18px; 
            font-weight: bold; 
            color: #2c3e50; 
            margin-bottom: 15px; 
            text-align: center;
        }
        
        .cart-total { 
            font-size: 24px; 
            font-weight: bold; 
            color: #8b4513; 
            text-align: center; 
            padding: 15px; 
            background: #f8f9fa; 
            border-radius: 8px;
        }
        
        @media (max-width: 768px) {
            .products-grid { grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); }
            .cart-summary { position: relative; top: auto; right: auto; margin-bottom: 20px; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>☕ BeCoffe POS</h1>
        <p>Interface simple avec images HSQL</p>
    </div>

    <div class="container">
        <?php if (!$pos->isConnected()): ?>
            <div class="status error">
                ❌ Erreur de connexion à la base de données HSQL
            </div>
        <?php else: ?>
            <div class="status success">
                ✅ Connexion HSQL réussie - <?php echo count($articles); ?> articles disponibles
            </div>

            <!-- Résumé du panier -->
            <div class="cart-summary">
                <div class="cart-title">🛒 Panier</div>
                <div class="cart-total" id="cart-total">0,00</div>
                <div style="text-align: center; margin-top: 10px;">
                    <span id="cart-items">0</span> article(s)
                </div>
            </div>

            <!-- Grille des produits -->
            <div class="products-grid">
                <?php foreach ($articles as $article): ?>
                    <div class="product-card">
                        <?php if ($article['has_image']): ?>
                            <img 
                                src="<?php echo $pos->getArticleImageUrl($article['IDarticles']); ?>" 
                                alt="<?php echo htmlspecialchars($article['designation']); ?>"
                                class="product-image"
                                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                            >
                            <div class="no-image" style="display: none;">
                                📷 Image non disponible
                            </div>
                        <?php else: ?>
                            <div class="no-image">
                                ☕ <?php echo htmlspecialchars($article['nom_categorie'] ?? 'Produit'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="product-info">
                            <?php if ($article['nom_categorie']): ?>
                                <div class="product-category">
                                    <?php echo htmlspecialchars($article['nom_categorie']); ?>
                                </div>
                            <?php endif; ?>

                            <div class="product-name">
                                <?php echo htmlspecialchars($article['designation']); ?>
                            </div>

                            <div class="product-price">
                                <?php echo formatPrice($pos->getArticlePrice($article['IDarticles'])); ?>
                            </div>

                            <div class="product-stock">
                                Stock: 
                                <span class="<?php 
                                    if ($article['quantite'] <= 0) echo 'stock-out';
                                    elseif ($article['quantite'] <= 5) echo 'stock-low';
                                    else echo 'stock-ok';
                                ?>">
                                    <?php echo $article['quantite']; ?>
                                    <?php if ($article['quantite'] <= 0): ?>
                                        (Rupture)
                                    <?php elseif ($article['quantite'] <= 5): ?>
                                        (Faible)
                                    <?php endif; ?>
                                </span>
                            </div>

                            <button 
                                class="add-btn" 
                                onclick="addToCart(<?php echo $article['IDarticles']; ?>, '<?php echo htmlspecialchars($article['designation']); ?>', <?php echo $pos->getArticlePrice($article['IDarticles']); ?>)"
                                <?php echo $article['quantite'] <= 0 ? 'disabled' : ''; ?>
                            >
                                <?php echo $article['quantite'] <= 0 ? '❌ Rupture de stock' : '➕ Ajouter au panier'; ?>
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php if (empty($articles)): ?>
                <div style="text-align: center; padding: 60px; color: #6c757d;">
                    <h3>☕ Aucun article disponible</h3>
                    <p>Vérifiez votre base de données HSQL</p>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <script>
        let cart = [];
        let cartTotal = 0;

        function addToCart(articleId, designation, price) {
            // Vérifier si l'article est déjà dans le panier
            const existingItem = cart.find(item => item.id === articleId);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: articleId,
                    name: designation,
                    price: price,
                    quantity: 1
                });
            }
            
            updateCartDisplay();
            
            // Animation de feedback
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '✅ Ajouté !';
            button.style.background = '#28a745';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '#28a745';
            }, 1000);
        }

        function updateCartDisplay() {
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            const totalPrice = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            
            document.getElementById('cart-items').textContent = totalItems;
            document.getElementById('cart-total').textContent =
                new Intl.NumberFormat('fr-FR', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }).format(totalPrice);
        }

        // Gestion des erreurs d'images
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Interface POS chargée avec <?php echo count($articles); ?> articles');
        });
    </script>
</body>
</html>
