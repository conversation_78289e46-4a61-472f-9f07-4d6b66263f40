<?php
/**
 * Affichage d'images depuis HSQL
 * Résout le problème VARBINARY(MAX) 
 */

require_once 'pos_config.php';

// Vérifier si un ID d'article est fourni
$articleId = isset($_GET['id']) ? intval($_GET['id']) : null;

if (!$articleId) {
    http_response_code(400);
    echo "ID d'article requis";
    exit;
}

if (!$pos->isConnected()) {
    http_response_code(500);
    echo "Erreur de connexion à la base de données";
    exit;
}

try {
    // Utiliser la nouvelle méthode pour récupérer l'image
    $imageData = $pos->getArticleImage($articleId);
    
    if ($imageData) {
        // Définir les en-têtes appropriés
        header('Content-Type: ' . $imageData['mime']);
        header('Content-Length: ' . strlen($imageData['data']));
        header('Cache-Control: public, max-age=3600'); // Cache 1 heure
        
        // Envoyer les données de l'image
        echo $imageData['data'];
    } else {
        // Image par défaut si pas d'image trouvée
        http_response_code(404);
        
        // Créer une image par défaut simple
        $defaultImage = imagecreate(200, 200);
        $bg = imagecolorallocate($defaultImage, 240, 240, 240);
        $text = imagecolorallocate($defaultImage, 100, 100, 100);
        
        imagestring($defaultImage, 3, 50, 90, 'Pas d\'image', $text);
        imagestring($defaultImage, 2, 70, 110, 'ID: ' . $articleId, $text);
        
        header('Content-Type: image/png');
        imagepng($defaultImage);
        imagedestroy($defaultImage);
    }
    
} catch (Exception $e) {
    error_log("Erreur image_display.php: " . $e->getMessage());
    http_response_code(500);
    echo "Erreur lors du chargement de l'image";
}
?>
