<?php
/**
 * API AJAX pour récupérer les détails d'un ticket
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

require_once 'pos_config.php';

// Vérifier la connexion
if (!$pos->isConnected()) {
    echo json_encode([
        'success' => false,
        'error' => 'Pas de connexion à la base de données'
    ]);
    exit;
}

// Récupérer l'ID du ticket
$ticketId = $_GET['ticket_id'] ?? '';

if (empty($ticketId)) {
    echo json_encode([
        'success' => false,
        'error' => 'ID ticket manquant'
    ]);
    exit;
}

try {
    // Requête exacte comme dans historique.php (équivalent WinDev)
    $sqlDetails = "SELECT 
        v.articles as article,
        v.quantite,
        v.total as sous_total,
        v.IDVteJour,
        v.IDarticle,
        v.NumCategories
    FROM VteJour v
    WHERE v.IDtickets = ?
    ORDER BY v.articles";
    
    $stmtDetails = $pos->pdo->prepare($sqlDetails);
    $stmtDetails->execute([$ticketId]);
    $detailsCommande = $stmtDetails->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($detailsCommande)) {
        // Succès - détails trouvés
        echo json_encode([
            'success' => true,
            'ticket_id' => $ticketId,
            'details' => $detailsCommande,
            'count' => count($detailsCommande)
        ]);
    } else {
        // Aucun détail trouvé
        echo json_encode([
            'success' => false,
            'ticket_id' => $ticketId,
            'error' => 'Aucun détail trouvé pour ce ticket',
            'details' => [],
            'count' => 0
        ]);
    }
    
} catch (Exception $e) {
    // Erreur de requête
    echo json_encode([
        'success' => false,
        'ticket_id' => $ticketId,
        'error' => 'Erreur de base de données: ' . $e->getMessage()
    ]);
}
?>
