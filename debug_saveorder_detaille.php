<?php
/**
 * Debug très détaillé de la méthode saveOrder
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Debug Détaillé saveOrder</h1>";

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'pos_config.php';

// Préparer un panier de test
$pos->clearCart();
$articles = $pos->getArticlesByCategory();
$testArticle = $articles[0];
$pos->addToCart($testArticle['IDarticles'], 1);
$cart = $pos->getCart();

echo "<h2>1. Données du panier</h2>";
echo "Articles dans le panier:<br>";
foreach ($cart as $id => $item) {
    echo "- ID: $id<br>";
    echo "- Article: {$item['article']['designation']}<br>";
    echo "- Quantité: {$item['quantity']}<br>";
    echo "- Prix: {$item['price']}<br>";
    echo "- Catégorie: {$item['article']['IDCategorie']}<br>";
    echo "- Cuisine: " . ($item['article']['Cuisine'] ?? 'NULL') . "<br>";
}

echo "<h2>2. Simulation manuelle de saveOrder</h2>";

try {
    $pdo = $pos->pdo;
    
    echo "<h3>2.1. Début de transaction</h3>";
    $pdo->beginTransaction();
    echo "✅ Transaction démarrée<br>";
    
    echo "<h3>2.2. Calcul du prochain NumTick</h3>";
    $sql = "SELECT MAX(NumTick) as last_num FROM tickets";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    $nextNumTick = ($result['last_num'] ?? 0) + 1;
    echo "✅ Prochain NumTick: $nextNumTick<br>";
    
    echo "<h3>2.3. Calcul du total</h3>";
    $total = $pos->getCartTotalTTC();
    echo "✅ Total calculé: $total<br>";
    
    echo "<h3>2.4. Préparation des données ticket</h3>";
    $dateNum = intval(date('Ymd'));
    $timeNum = intval(date('His'));
    $serverId = 1;
    $tableId = 103;
    $paymentCode = 'ESP';
    
    echo "- Date: $dateNum<br>";
    echo "- Heure: $timeNum<br>";
    echo "- Serveur: $serverId<br>";
    echo "- Table: $tableId<br>";
    echo "- Total: $total<br>";
    echo "- Paiement: $paymentCode<br>";
    
    echo "<h3>2.5. INSERT ticket</h3>";
    $sql = "INSERT INTO tickets (NumTick, IDServeur, IDTables, DATE, heure, total, typepaye, Djr, ImpTick) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $insertData = [
        intval($nextNumTick),
        intval($serverId),
        intval($tableId),
        $dateNum,
        $timeNum,
        floatval($total),
        $paymentCode,
        1,
        1
    ];
    
    echo "Données INSERT ticket: " . implode(', ', $insertData) . "<br>";
    
    $stmt = $pdo->prepare($sql);
    $success = $stmt->execute($insertData);
    
    if ($success) {
        echo "✅ Ticket inséré avec succès<br>";
    } else {
        $errorInfo = $stmt->errorInfo();
        echo "❌ Erreur INSERT ticket: " . print_r($errorInfo, true) . "<br>";
        throw new Exception("Erreur INSERT ticket: " . $errorInfo[2]);
    }
    
    echo "<h3>2.6. Récupération ID ticket</h3>";
    $ticketId = $pdo->lastInsertId();
    
    if (!$ticketId) {
        $sql = "SELECT IDtickets FROM tickets WHERE NumTick = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$nextNumTick]);
        $result = $stmt->fetch();
        $ticketId = $result['IDtickets'] ?? null;
    }
    
    if ($ticketId) {
        echo "✅ Ticket ID récupéré: $ticketId<br>";
    } else {
        throw new Exception("Impossible de récupérer l'ID du ticket");
    }
    
    echo "<h3>2.7. INSERT détails VteJour</h3>";
    $sql = "INSERT INTO VteJour (articles, quantite, total, IDarticle, NumCategories, Cuisine, IDtickets) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($sql);
    
    foreach ($cart as $articleId => $item) {
        $article = $item['article'];
        $quantity = $item['quantity'];
        $itemTotal = $item['price'] * $quantity;
        
        $vteData = [
            $article['designation'],
            $quantity,
            $itemTotal,
            $articleId,
            $article['IDCategorie'],
            $article['Cuisine'] ?? '',
            $ticketId
        ];
        
        echo "Insertion VteJour pour article $articleId:<br>";
        echo "- Données: " . implode(', ', $vteData) . "<br>";
        
        $success = $stmt->execute($vteData);
        
        if ($success) {
            echo "✅ Détail inséré pour article $articleId<br>";
        } else {
            $errorInfo = $stmt->errorInfo();
            echo "❌ Erreur INSERT VteJour pour article $articleId:<br>";
            echo "Code: " . $errorInfo[0] . "<br>";
            echo "Code driver: " . $errorInfo[1] . "<br>";
            echo "Message: " . $errorInfo[2] . "<br>";
            throw new Exception("Erreur INSERT VteJour: " . $errorInfo[2]);
        }
    }
    
    echo "<h3>2.8. Validation transaction</h3>";
    $pdo->commit();
    echo "✅ Transaction validée<br>";
    
    echo "<h3>2.9. Vérification finale</h3>";
    $sql = "SELECT COUNT(*) as count FROM tickets WHERE IDtickets = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$ticketId]);
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        echo "✅ Ticket confirmé en base<br>";
    } else {
        echo "❌ Ticket non trouvé après commit<br>";
    }
    
    $sql = "SELECT COUNT(*) as count FROM VteJour WHERE IDtickets = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$ticketId]);
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        echo "✅ Détails confirmés en VteJour: " . $result['count'] . " ligne(s)<br>";
    } else {
        echo "❌ Aucun détail trouvé en VteJour<br>";
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎉 SUCCÈS COMPLET !</h3>";
    echo "<strong>Ticket créé avec ID: $ticketId</strong><br>";
    echo "La simulation manuelle fonctionne parfaitement !";
    echo "</div>";
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
        echo "Transaction annulée<br>";
    }
    
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ ERREUR CAPTURÉE:</h4>";
    echo "<strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>Fichier:</strong> " . $e->getFile() . "<br>";
    echo "<strong>Ligne:</strong> " . $e->getLine() . "<br>";
    echo "</div>";
}

echo "<h2>3. Comparaison avec la méthode originale</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<h3>🤔 Analyse:</h3>";
echo "<p>Si la simulation manuelle ci-dessus fonctionne mais que saveOrder() échoue, ";
echo "cela signifie qu'il y a une différence subtile dans la méthode originale.</p>";

echo "<h3>🔍 Vérifications à faire:</h3>";
echo "<ul>";
echo "<li>Vérifier les logs PHP (error_log)</li>";
echo "<li>Comparer les données exactes utilisées</li>";
echo "<li>Vérifier les permissions de transaction</li>";
echo "<li>Vérifier les contraintes de base de données</li>";
echo "</ul>";
echo "</div>";

echo "<h2>✅ Debug terminé</h2>";
echo "<p>Si la simulation manuelle fonctionne, nous pouvons identifier pourquoi saveOrder() échoue.</p>";
?>
